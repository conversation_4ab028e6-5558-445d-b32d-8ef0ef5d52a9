from botcity.web.browsers.chrome import default_options
from botcity.web import *
from botcity.core import *
from datetime import datetime
from botcity.plugins.excel import *
from botcity.plugins.files import *
import os
import shutil
from dotenv import load_dotenv
import pytesseract
from PIL import Image, ImageGrab
import cv2
import re
from watchdog.observers import *
from watchdog.events import *
import pdfplumber
import win32com.client
import logging
from logging.handlers import RotatingFileHandler
import time
import win32api
import win32con
import win32gui
from openpyxl import load_workbook
import pyautogui
import psutil
import mysql.connector

class Bot:
    def bot(self):
        # Custom Python Code Activity
        # Displayname: CodePython
        # Configuração do Logger dentro do método bot
        log_dir = "C:\\RPA\\AtualizacaoCadastral\\logs"
                
        # Gerar nome do arquivo de log com data e hora para garantir que o log não sobrescreva
        log_filename = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")  # Formato de data e hora: YYYY-MM-DD_HH-MM-SS
        log_file = os.path.join(log_dir, f"bot_log_{log_filename}.log")  # Nome do arquivo com data e hora
        
        # Criar o diretório de logs caso não exista
        if not os.path.exists(log_dir):
             os.makedirs(log_dir)
        
        # Configurar o logger
        loggerBot = logging.getLogger("Description logger")
        loggerBot.setLevel(logging.DEBUG)
        
        # Configurar o handler com arquivo rotativo
        rotating_handler = RotatingFileHandler(log_file, maxBytes=1000000, backupCount=3)
        rotating_handler.setLevel(logging.DEBUG)
        
        # Configurar o formato do log
        formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        rotating_handler.setFormatter(formatter)
        loggerBot.addHandler(rotating_handler)
        
        # Adicionar handler para exibir no console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        loggerBot.addHandler(console_handler)

        # Custom Python Code Activity
        # Displayname: CONEXAO COM O BANCO DE DADOS
        # Tentativa de conexão com o banco de dados
        try:
            conexao = mysql.connector.connect(
                host='rpa.sicoobcredilivre.com.br',
                user='rpa',
                password='sicoob@123',
                database='rpa',
            )
            cursor = conexao.cursor()
        
            # Registrar sucesso no log
            loggerBot.info("Conexão com o banco de dados estabelecida com sucesso.")
        
        except mysql.connector.Error as err:
            # Registrar erro no log
            loggerBot.error(f"Erro ao conectar com o banco de dados: {err}")

        # Custom Python Code Activity
        # Displayname: FUNÇÃO ATUALIZAR RENDA
        def atualizar_renda():
            # Find Activity
            cpfNaBase = deskBot.find("cpfNaBase", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
            # If Activity
            if cpfNaBase:
                # Custom Python Code Activity
                loggerBot.error("CPF não encontrado na base da cooperativa.")
        
                # Find Activity
                okCPFnaBase = deskBot.find("okCPFnaBase", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
                # Find And Click Activity
                deskBot.click()
        
                # Wait Activity
                deskBot.wait(2000)
        
                # Find And Click Activity
                deskBot.click()
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Custom Python Code Activity
            loggerBot.error("Realizando alteração da renda")
        
            # Custom Python Code Activity
            # Clica em Empregador
            x, y = 243, 324
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.space(wait=0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Clica em Tipo de Renda
            x, y = 250, 354
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Type Into Activity
            deskBot.paste(text="OUTROS", wait=0)
        
            # Clica em Tipo de Renda novamente
            x, y = 250, 354
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
            deskBot.control_c(wait=0)
            deskBot.control_v(wait=0)
        
            # Clica em Renda Bruta Mensal
            x, y = 232, 382
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Função melhorada para extrair renda do PDF com tratamento de encoding
            def extrair_renda_pdf(extractTextResult):
                """
                Função melhorada para extrair valor da renda do texto do PDF com tratamento de encoding
                """
                try:
                    # Limpar caracteres especiais e normalizar o texto
                    import unicodedata
                    
                    # Remover caracteres de controle e especiais
                    texto_limpo = ''.join(char for char in extractTextResult if unicodedata.category(char)[0] != 'C')
                    
                    # Normalizar unicode
                    texto_limpo = unicodedata.normalize('NFKD', texto_limpo)
                    
                    # Remover acentos e caracteres especiais
                    texto_limpo = ''.join(char for char in texto_limpo if ord(char) < 128 or char.isalnum() or char in ' .,R$')
                    
                    # Normalizar espaços
                    texto_limpo = ' '.join(texto_limpo.split())
                    
                except Exception as e:
                    print(f"Erro na limpeza do texto: {e}")
                    # Fallback: usar texto original com limpeza básica
                    texto_limpo = ' '.join(str(extractTextResult).split())
                
                # Múltiplos padrões de regex para diferentes formatos
                padroes_renda = [
                    # Padrão original: "Renda Estimada ... R$ valor"
                    r"Renda Estimada.*?R\$\s*(\d[\d\.,]*)",
                    
                    # Padrão para quando está em linhas separadas: "Renda Estimada" seguido de "R$ valor"
                    r"Renda [Ee]stimada.*?R\$\s*(\d[\d\.,]*)",
                    
                    # Padrão mais flexível: qualquer "R$ valor" após "Renda"
                    r"Renda.*?R\$\s*(\d[\d\.,]*)",
                    
                    # Padrão específico para o caso atual: "R$ valor" seguido de "Renda estimada"
                    r"R\$\s*(\d[\d\.,]*)\s*Renda estimada",
                    
                    # Padrão ainda mais específico para capturar "R$ 1411" isolado
                    r"R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)",
                    
                    # Padrão genérico para R$ seguido de números próximo a "Renda"
                    r"(?:Renda|renda).*?R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)",
                    
                    # Padrão reverso: R$ valor antes de "Renda"
                    r"R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)"
                ]
                
                for padrao in padroes_renda:
                    try:
                        match = re.search(padrao, texto_limpo, re.IGNORECASE)
                        if match:
                            valor_bruto = match.group(1)
                            # Limpar pontos de milhares e vírgulas
                            valor_limpo = valor_bruto.replace('.', '').replace(',', '.')
                            try:
                                return float(valor_limpo)
                            except ValueError:
                                continue
                    except Exception as e:
                        print(f"Erro no padrão {padrao}: {e}")
                        continue
                
                return None
        
            # Ler PDF e extrair o valor da Renda Serasa
            try:
                print(f"Conteúdo da lista 'linha': {linha}")
            except UnicodeEncodeError:
                print("Conteúdo da lista 'linha': [dados com caracteres especiais]")
        
            # ============================================================================
            # OBTER RENDA VIA API - VERSÃO OTIMIZADA
            # ============================================================================

            valorRenda = ""

            # Usar renda da API ou ler do arquivo JSON
            if 'renda_valor' in locals() and renda_valor:
                # Usar valor direto da API
                renda_final = min(renda_valor, 10000.00)  # Aplicar limite
                valorRenda = f"{renda_final:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
                loggerBot.info(f"Renda da API formatada: {valorRenda}")

            else:
                # Ler do arquivo JSON
                json_path = f"C:\\RPA\\AtualizacaoCadastral\\consultas\\[{linha[1]}].json"
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        dados = json.load(f)
                        renda_api = dados.get('renda_estimada', 0)

                        if renda_api > 0:
                            renda_final = min(renda_api, 10000.00)
                            valorRenda = f"{renda_final:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
                            loggerBot.info(f"Renda do JSON: {valorRenda}")

                except Exception as e:
                    loggerBot.error(f"Erro ao ler JSON para CPF {linha[1]}: {str(e)}")

            # Verificar se obtivemos valor válido
            if not valorRenda:
                loggerBot.error(f"Falha ao obter renda para CPF {linha[1]}")
                valorRenda = "0,00"  # Valor padrão para evitar erro
        
            # Type Into Activity
            deskBot.paste(text=valorRenda, wait=0)
        
            # Clica em Descrição
            x, y = 323, 474
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Type Into Activity
            deskBot.paste(text="RENDA ESTIMADA PROVENIENTE DE BASE ESPECIALIZADA EM ENRIQUECIMENTO DE INFORMAÇÕES, CONFORME CONSULTA SERASA PRÓ EM ANEXO.", wait=0)
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Seleciona o tipo de renda como Variável
            x, y = 170, 569
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Clica na aba Documentos
            x, y = 179, 213
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Clica no botão Adicionar
            x, y = 1645, 286
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(4000)
        
            # Clica no botão Adicionar Arquivos
            x, y = 520, 800
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(5000)
        
            # Find Activity
            caminhoPDF = deskBot.find("caminhoPDF", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Type Into Activity
            deskBot.paste(text="C:\RPA\AtualizacaoCadastral\consultas", wait=0)
        
            # Send Hotkey Activity
            deskBot.key_enter(wait=0)
        
            # Find Activity
            nomeFile = deskBot.find("nomeFile", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Type Into Activity
            deskBot.paste(text="["+linha[1]+"].pdf", wait=0)
        
            # Send Hotkey Activity
            deskBot.key_enter(wait=0)
        
            # Find Activity
            okAnexar = deskBot.find("okAnexar", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Clica no botão Enviar para enviar documentação
            x, y = 1472, 961
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(5000)
        
            # Clica no botão Gravar
            x, y = 1888, 916
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(4000)
        
            # Find Activity
            valorRendaIncomp = deskBot.find("valorRendaIncomp", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
            # If Activity
            if valorRendaIncomp:
                btOkRendaIncomp = deskBot.find("btOkRendaIncomp", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
                deskBot.click()
                deskBot.wait(1500)
        
            # Clica no botão Parceiro de Negócios
            x, y = 22, 239
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Clica no botão Parceiro de Negócios novamente
            x, y = 22, 239
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Find Activity
            encAutorizacao = deskBot.find("encAutorizacao", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(4000)
        
            # Clica em Encaminhar para Autorização
            x, y = 1828, 229
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Find Activity
            okEncaminhar = deskBot.find("okEncaminhar", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(6000)
        
            # Clica em Fechar a Plataforma de Atendimento
            x, y = 1896, 10
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(5000)
            
            return valorRenda

        # Custom Python Code Activity
        # Displayname: FUNÇÃO NOVA RENDA
        def nova_renda():
            # Find Activity
            cpfNaBase = deskBot.find("cpfNaBase", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
            # If Activity
            if cpfNaBase:
                # Custom Python Code Activity
                loggerBot.error("CPF não encontrado na base da cooperativa.")
        
                # Find Activity
                okCPFnaBase = deskBot.find("okCPFnaBase", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
                # Find And Click Activity
                deskBot.click()
        
                # Wait Activity
                deskBot.wait(2000)
        
                # Find And Click Activity
                deskBot.click()
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Custom Python Code Activity
            loggerBot.error("Realizando inclusão da renda")
        
            # Custom Python Code Activity
            loggerBot.error("Selecionando Empregador")
            
            # Clica em Empregador
            x, y = 267, 269
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.space(wait=0)
        
            # Wait Activity
            deskBot.wait(1000)
            
            loggerBot.error("Selecionando Tipo de Renda")
        
            # Clica em Tipo de Renda
            x, y = 296, 296
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Type Into Activity
            deskBot.paste(text="OUTROS", wait=0)
        
            # Clica em Tipo de Renda novamente
            x, y = 296, 296
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
            deskBot.control_c(wait=0)
            deskBot.control_v(wait=0)
            
            loggerBot.error("Selecionando Renda Bruta Mensal")
        
            # Clica em Renda Bruta Mensal
            x, y = 269, 325
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Função melhorada para extrair renda do PDF com tratamento de encoding
            def extrair_renda_pdf(extractTextResult):
                """
                Função melhorada para extrair valor da renda do texto do PDF com tratamento de encoding
                """
                try:
                    # Limpar caracteres especiais e normalizar o texto
                    import unicodedata
                    
                    # Remover caracteres de controle e especiais
                    texto_limpo = ''.join(char for char in extractTextResult if unicodedata.category(char)[0] != 'C')
                    
                    # Normalizar unicode
                    texto_limpo = unicodedata.normalize('NFKD', texto_limpo)
                    
                    # Remover acentos e caracteres especiais
                    texto_limpo = ''.join(char for char in texto_limpo if ord(char) < 128 or char.isalnum() or char in ' .,R$')
                    
                    # Normalizar espaços
                    texto_limpo = ' '.join(texto_limpo.split())
                    
                except Exception as e:
                    print(f"Erro na limpeza do texto: {e}")
                    # Fallback: usar texto original com limpeza básica
                    texto_limpo = ' '.join(str(extractTextResult).split())
                
                # Múltiplos padrões de regex para diferentes formatos
                padroes_renda = [
                    # Padrão original: "Renda Estimada ... R$ valor"
                    r"Renda Estimada.*?R\$\s*(\d[\d\.,]*)",
                    
                    # Padrão para quando está em linhas separadas: "Renda Estimada" seguido de "R$ valor"
                    r"Renda [Ee]stimada.*?R\$\s*(\d[\d\.,]*)",
                    
                    # Padrão mais flexível: qualquer "R$ valor" após "Renda"
                    r"Renda.*?R\$\s*(\d[\d\.,]*)",
                    
                    # Padrão específico para o caso atual: "R$ valor" seguido de "Renda estimada"
                    r"R\$\s*(\d[\d\.,]*)\s*Renda estimada",
                    
                    # Padrão ainda mais específico para capturar "R$ 1411" isolado
                    r"R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)",
                    
                    # Padrão genérico para R$ seguido de números próximo a "Renda"
                    r"(?:Renda|renda).*?R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)",
                    
                    # Padrão reverso: R$ valor antes de "Renda"
                    r"R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)"
                ]
                
                for padrao in padroes_renda:
                    try:
                        match = re.search(padrao, texto_limpo, re.IGNORECASE)
                        if match:
                            valor_bruto = match.group(1)
                            # Limpar pontos de milhares e vírgulas
                            valor_limpo = valor_bruto.replace('.', '').replace(',', '.')
                            try:
                                return float(valor_limpo)
                            except ValueError:
                                continue
                    except Exception as e:
                        print(f"Erro no padrão {padrao}: {e}")
                        continue
                
                return None
        
            # Ler PDF e extrair o valor da Renda Serasa
            try:
                print(f"Conteúdo da lista 'linha': {linha}")
            except UnicodeEncodeError:
                print("Conteúdo da lista 'linha': [dados com caracteres especiais]")
        
            # ============================================================================
            # OBTER RENDA VIA API - VERSÃO OTIMIZADA (2ª OCORRÊNCIA)
            # ============================================================================

            valorRenda = ""

            # Usar renda da API ou ler do arquivo JSON
            if 'renda_valor' in locals() and renda_valor:
                # Usar valor direto da API
                renda_final = min(renda_valor, 10000.00)  # Aplicar limite
                valorRenda = f"{renda_final:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
                loggerBot.info(f"Renda da API formatada: {valorRenda}")

            else:
                # Ler do arquivo JSON
                json_path = f"C:\\RPA\\AtualizacaoCadastral\\consultas\\[{linha[1]}].json"
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        dados = json.load(f)
                        renda_api = dados.get('renda_estimada', 0)

                        if renda_api > 0:
                            renda_final = min(renda_api, 10000.00)
                            valorRenda = f"{renda_final:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
                            loggerBot.info(f"Renda do JSON: {valorRenda}")

                except Exception as e:
                    loggerBot.error(f"Erro ao ler JSON para CPF {linha[1]}: {str(e)}")

            # Verificar se obtivemos valor válido
            if not valorRenda:
                loggerBot.error(f"Falha ao obter renda para CPF {linha[1]}")
                valorRenda = "0,00"  # Valor padrão para evitar erro
        
            # Type Into Activity
            deskBot.paste(text=valorRenda, wait=0)
        
            # Clica em Descrição
            x, y = 323, 414
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(1000)
        
            # Send Hotkey Activity
            deskBot.control_a(wait=0)
        
            # Type Into Activity
            deskBot.paste(text="RENDA ESTIMADA PROVENIENTE DE BASE ESPECIALIZADA EM ENRIQUECIMENTO DE INFORMAÇÕES, CONFORME CONSULTA SERASA PRÓ EM ANEXO.", wait=0)
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Seleciona o tipo de renda como Variável
            x, y = 170, 569
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Clica na aba Documentos
            x, y = 179, 213
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Clica no botão Adicionar
            x, y = 1645, 286
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(4000)
        
            # Clica no botão Adicionar Arquivos
            x, y = 520, 800
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(5000)
        
            # Find Activity
            caminhoPDF = deskBot.find("caminhoPDF", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Type Into Activity
            deskBot.paste(text="C:\RPA\AtualizacaoCadastral\consultas", wait=0)
        
            # Send Hotkey Activity
            deskBot.key_enter(wait=0)
        
            # Find Activity
            nomeFile = deskBot.find("nomeFile", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Type Into Activity
            deskBot.paste(text="["+linha[1]+"].pdf", wait=0)
        
            # Send Hotkey Activity
            deskBot.key_enter(wait=0)
        
            # Find Activity
            okAnexar = deskBot.find("okAnexar", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(2000)
        
            # Clica no botão Enviar para enviar documentação
            x, y = 1472, 961
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(5000)
        
            # Clica no botão Gravar
            x, y = 1888, 916
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(4000)
        
            # Find Activity
            valorRendaIncomp = deskBot.find("valorRendaIncomp", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
        
            # If Activity
            if valorRendaIncomp:
                btOkRendaIncomp = deskBot.find("btOkRendaIncomp", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)
                deskBot.click()
                deskBot.wait(1500)
        
            # Clica no botão Parceiro de Negócios
            x, y = 22, 239
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Clica no botão Parceiro de Negócios novamente
            x, y = 22, 239
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Find Activity
            encAutorizacao = deskBot.find("encAutorizacao", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(4000)
        
            # Clica em Encaminhar para Autorização
            x, y = 1828, 229
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(3000)
        
            # Find Activity
            okEncaminhar = deskBot.find("okEncaminhar", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Wait Activity
            deskBot.wait(6000)
        
            # Clica em Fechar a Plataforma de Atendimento
            x, y = 1896, 10
            win32api.SetCursorPos((x, y))
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)
        
            # Wait Activity
            deskBot.wait(5000)
            
            return valorRenda

        # Custom Python Code Activity
        # Displayname: FUNÇÃO EXCLUIR RENDA
        def excluir_renda():
            btExcluirRenda = deskBot.find("btExcluirRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Find Activity
            msgExclusao = deskBot.find("msgExclusao", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find Activity
            confExclusao = deskBot.find("confExclusao", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)
        
            # Find And Click Activity
            deskBot.click()
        
            # Find Activity
            naoExcluir = deskBot.find("naoExcluir", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

        # Sequence: Try Structure

        # Try Activity
        # Displayname: Try
        try:
            # Sequence: Body

            # Sequence: Sequencia do RPA

            # Custom Python Code Activity
            # Displayname: CodePython
            loggerBot.info("Iniciando execução do RPA...")

            # Sequence: Configuração .ENV

            #  Load DotENV Activity
            # Displayname: Load_DotENV
            load_dotenv()

            # Get DotEnv Value Activity
            # Displayname: Get_DotEnv_Value
            user_rpai = os.getenv("USER_RPAI")

            # Get DotEnv Value Activity
            # Displayname: Get_DotEnv_Value
            senha_rpai = os.getenv("SENHA_RPAI")

            # Get DotEnv Value Activity
            # Displayname: Get_DotEnv_Value
            user_serasa = os.getenv("USER_SERASA")

            # Get DotEnv Value Activity
            # Displayname: Get_DotEnv_Value
            senha_serasa = os.getenv("SENHA_SERASA")

            # Open Application Activity
            # Displayname: OpenApplication
            title = "Sisbr 2.0"
            executablePath = "C:\\Sisbr 2.0\\Sisbr 2.0.exe"
            deskBot = DesktopBot()
            deskBot.execute(executablePath)
            deskBot.connect_to_app(backend=Backend.WIN_32, timeout=60000, title=title, path=executablePath)
            popup_Window = deskBot.find_app_window(waiting_time=10000, title=title)

            # Custom Python Code Activity
            # Displayname: CodePython
            loggerBot.info("Abrindo aplicação Sisbr 2.0")

            # Wait Activity
            # Displayname: Wait
            deskBot.wait(15000)

            # Custom Python Code Activity
            # Displayname: Click na coordenada Usuário do Sisbr
            x, y = 1048, 481
            
            # Movimenta o cursor para as coordenadas e realiza o clique
            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
            
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

            # Wait Activity
            # Displayname: Wait
            deskBot.wait(1000)

            # Send Hotkey Activity
            # Displayname: Send_Hotkey
            deskBot.control_a(wait=0)

            # Type Into Activity
            # Displayname: Type_Into
            deskBot.paste(text=user_rpai, wait=0)

            # Custom Python Code Activity
            # Displayname: Click na coordenada Senha do Sisbr
            x, y = 1048, 509
            
            # Movimenta o cursor para as coordenadas e realiza o clique
            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
            
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

            # Type Into Activity
            # Displayname: Type_Into
            deskBot.paste(text=senha_rpai, wait=0)

            # Find Activity
            # Displayname: Botão Logar
            btnLogar = deskBot.find("btnLogar", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

            # Click Activity
            # Displayname: Click
            deskBot.click()

            # Wait Activity
            # Displayname: Wait
            deskBot.wait(10000)

            # Sequence: DEPURAÇÃO LOGIN SISBR

            # Find Activity
            # Displayname: Find
            dep_login = deskBot.find("dep_login", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)

            # Sequence: Conditional Structure

            # If Activity
            # Displayname: If Condition
            if dep_login:
                # Sequence: Body

                # Custom Python Code Activity
                # Displayname: CodePython
                loggerBot.info("Login realizado com sucesso na aplicação Sisbr 2.0")


            # Else Activity
            # Displayname: Else
            else:
                # Sequence: Body

                # Custom Python Code Activity
                # Displayname: CodePython
                loggerBot.info("Falaha ao tentar realizar o login na aplicação Sisbr 2.0")


            # ============================================================================
            # BACKUP - SEQUENCIA SERASA ORIGINAL (COMENTADA)
            # ============================================================================

            # # Sequence: SEQUENCIA SERASA
            #
            # # Open Browser Activity
            # # Displayname: OpenBrowser
            # webBot = WebBot()
            # webBot.driver_path = "C:\\RPA\\AtualizacaoCadastral\\Drivers\\chromedriver-win64\\chromedriver.exe"
            # webBot.browser = Browser.CHROME
            # webBot.headless = False
            # webBotDef_options = default_options()
            # webBotDef_options.add_argument("--page-load-strategy=Normal")
            # webBot.options = webBotDef_options
            # webBot.browse("https://www.serasaexperian.com.br/meus-produtos/login")
            #
            # # Custom Python Code Activity
            # # Displayname: CodePython
            # loggerBot.info("Abrindo Google Chrome")
            #
            # # Custom Python Code Activity
            # # Displayname: CodePython
            # loggerBot.info("Abrindo site do Serasa")
            #
            # # Wait Activity
            # # Displayname: Wait
            # webBot.wait(5000)
            #
            # # Find Element Activity
            # # Displayname: Campo Usuário
            # iduserLogon = webBot.find_element(selector="userLogon", by=By.ID, waiting_time=1000, ensure_visible=False, ensure_clickable=False)
            #
            # # Click Activity
            # # Displayname: Click
            # iduserLogon.click()
            #
            # # Type Into Activity
            # # Displayname: Type_Into
            # webBot.paste(text=user_serasa, wait=0)
            #
            # # Find Element Activity
            # # Displayname: Campo Senha
            # iduserPassword = webBot.find_element(selector="userPassword", by=By.ID, waiting_time=1000, ensure_visible=False, ensure_clickable=False)
            #
            # # Click Activity
            # # Displayname: Click
            # iduserPassword.click()
            #
            # # Type Into Activity
            # # Displayname: Type_Into
            # webBot.paste(text=senha_serasa, wait=0)
            #
            # # Wait Activity
            # # Displayname: Wait
            # webBot.wait(2000)
            #
            # # Find Element Activity
            # # Displayname: Botão Acessar
            # btn_Acessar = webBot.find_element(selector="/html/body/app-root/app-auth/ca-authentication/ca-login/main/section/div/section/div/form/div[3]/div[2]/seds-basic-button/seds-core-button/container-element/button/span[2]/div/span", by=By.XPATH, waiting_time=1000, ensure_visible=False, ensure_clickable=False)
            #
            # # Click Activity
            # # Displayname: Click
            # btn_Acessar.click()
            #
            # # Wait Activity
            # # Displayname: Wait
            # webBot.wait(5000)
            #
            # # Find Element Activity
            # # Displayname: Botão Pular Tour
            # PularTour = webBot.find_element(selector="/html/body/div/div[2]/div/mat-dialog-container/div/div/div/mat-dialog-content/div/div[2]/div/a", by=By.XPATH, waiting_time=1000, ensure_visible=False, ensure_clickable=False)
            #
            # # Sequence: Conditional Structure
            #
            # # If Activity
            # # Displayname: If Condition
            # if PularTour:
            #     # Sequence: Body
            #
            #     # Custom Python Code Activity
            #     # Displayname: CodePython
            #     loggerBot.info("Login realizado com sucesso no Serasa!")
            #
            #
            # # Else Activity
            # # Displayname: Else
            # else:
            #     # Sequence: Body
            #
            #     # Custom Python Code Activity
            #     # Displayname: CodePython
            #     loggerBot.error("Falha ao realizar o login no Serasa")
            #
            #
            # # Click Activity
            # # Displayname: Click
            # PularTour.click()
            #
            # # Wait Activity
            # # Displayname: Wait
            # webBot.wait(3000)
            #
            # # Find Element Activity
            # # Displayname: Aba Pessoas
            # AbaPessoas = webBot.find_element(selector="/html/body/app-root/app-dashboard/div/ca-search/div/div/div/div/div[2]/label/button", by=By.XPATH, waiting_time=1000, ensure_visible=False, ensure_clickable=False)
            #
            # # Click Activity
            # # Displayname: Click
            # AbaPessoas.click()
            #
            # # Custom Python Code Activity
            # # Displayname: CodePython
            # loggerBot.info("Acessando aba Pessoas")
            #
            # # Wait Activity
            # # Displayname: Wait
            # webBot.wait(3000)
            #
            # # Find Element Activity
            # # Displayname: Card Credit Bureau
            # CreditBureau = webBot.find_element(selector="/html/body/app-root/app-dashboard/div/ca-card-list-view/div/main/section/div/ca-card-product[4]/div/div/div[3]/button", by=By.XPATH, waiting_time=1000, ensure_visible=False, ensure_clickable=False)
            #
            # # Click Activity
            # # Displayname: Click
            # CreditBureau.click()
            #
            # # Custom Python Code Activity
            # # Displayname: CodePython
            # loggerBot.info("Acessando Credit Bureau")
            #
            # # Wait Activity
            # # Displayname: Wait
            # webBot.wait(3000)
            #
            # # Custom Python Code Activity
            # # Displayname: CodePython
            # loggerBot.info("Verificando se a opção Capacidade Mensal de Pagamento está marcada")
            #
            # # Custom Python Code Activity
            # # Displayname: Mover PDF para pasta consultas e renomear
            # # MonitorFolderEventHandler Activity
            # class MonitorFolderEventHandler(FileSystemEventHandler):
            #     def __init__(self, callback):
            #         self.callback = callback
            #
            #     def on_created(self, event):
            #         self.callback(event.src_path)
            #
            # # MonitorFolder Activity
            # class MonitorFolder:
            #     def __init__(self, folder_path, recursive=False):
            #         self.folder_path = folder_path
            #         self.recursive = recursive
            #
            #     def start_monitoring(self, callback):
            #         event_handler = MonitorFolderEventHandler(callback)
            #         observer = Observer()
            #         observer.schedule(event_handler, self.folder_path, self.recursive)
            #         observer.start()
            #
            #     def executeReturn(pathFile):
            #         time.sleep(2)  # Allow some time for the file operation
            #
            #         # File Handler Activity - Get the file name and destination path
            #         fileName = os.path.basename(pathFile)
            #         newPath = os.path.join("C:\\RPA\\AtualizacaoCadastral\\consultas", fileName)
            #
            #         # Check if file already exists in the destination folder
            #         if os.path.exists(newPath):
            #             # If the file exists, remove the existing file
            #             os.remove(newPath)
            #             loggerBot.info(f"Arquivo existente {fileName} removido.")
            #
            #         # Move the new file to the target folder
            #         shutil.move(pathFile, newPath)
            #
            #         # Custom Python Code Activity
            #         # Renaming the moved file
            #         newFileName = f"[{linha[1]}].pdf"  # Renaming based on 'linha[1]'
            #         newPathRenamed = os.path.join("C:\\RPA\\AtualizacaoCadastral\\consultas", newFileName)
            #
            #         # Check if the renamed file already exists
            #         if os.path.exists(newPathRenamed):
            #             # If the renamed file exists, remove it
            #             os.remove(newPathRenamed)
            #             loggerBot.info(f"Arquivo {newFileName} já existia na pasta e foi removido.")
            #
            #         # Rename the file
            #         os.rename(newPath, newPathRenamed)
            #         loggerBot.info(f"Arquivo renomeado para: {newFileName}")
            #
            # # Start monitoring the folder
            # instanceMonitor = MonitorFolder("C:\\RPA\\AtualizacaoCadastral")
            # instanceMonitor.start_monitoring(MonitorFolder.executeReturn)
            #
            # # Wait Activity - Optional if needed to allow the monitoring to run
            # webBot.wait(5000)
            #
            # # Custom Python Code Activity
            # # Displayname: CodePython
            # loggerBot.info("Iniciando processamento de CPFs...")

            # ============================================================================
            # NOVA IMPLEMENTAÇÃO - CONSULTA VIA API SERASA
            # ============================================================================

            # Sequence: CONSULTA API SERASA

            # Custom Python Code Activity
            # Displayname: Configuração API Serasa
            import requests
            import json
            from datetime import datetime

            # Configurações da API Serasa
            SERASA_API_BASE_URL = "https://api.serasaexperian.com.br"
            SERASA_API_TOKEN = os.getenv("SERASA_API_TOKEN")  # Token de acesso da API
            SERASA_CLIENT_ID = os.getenv("SERASA_CLIENT_ID")  # Client ID
            SERASA_CLIENT_SECRET = os.getenv("SERASA_CLIENT_SECRET")  # Client Secret

            # Função para obter token de acesso OAuth2
            def obter_token_acesso():
                """
                Obtém token de acesso OAuth2 para a API do Serasa
                """
                try:
                    url_token = f"{SERASA_API_BASE_URL}/oauth/token"

                    headers = {
                        "Content-Type": "application/x-www-form-urlencoded"
                    }

                    data = {
                        "grant_type": "client_credentials",
                        "client_id": SERASA_CLIENT_ID,
                        "client_secret": SERASA_CLIENT_SECRET,
                        "scope": "relatorio-avancado-pf"
                    }

                    response = requests.post(url_token, headers=headers, data=data, timeout=30)

                    if response.status_code == 200:
                        token_data = response.json()
                        access_token = token_data.get("access_token")
                        loggerBot.info("Token de acesso obtido com sucesso")
                        return access_token
                    else:
                        loggerBot.error(f"Erro ao obter token: {response.status_code} - {response.text}")
                        return None

                except Exception as e:
                    loggerBot.error(f"Erro na autenticação API Serasa: {str(e)}")
                    return None

            # Função para consultar renda via API - OTIMIZADA
            def consultar_renda_api(cpf, access_token):
                """
                Consulta renda estimada PF via API - Versão otimizada e direta
                """
                try:
                    url_consulta = f"{SERASA_API_BASE_URL}/v1/relatorio-avancado-pf"

                    headers = {
                        "Authorization": f"Bearer {access_token}",
                        "Content-Type": "application/json"
                    }

                    # Payload simplificado - apenas o essencial
                    payload = {
                        "documento": cpf,
                        "tipoDocumento": "CPF",
                        "produtos": ["renda-estimada-pf"]
                    }

                    loggerBot.info(f"Consultando renda para CPF: {cpf}")

                    response = requests.post(url_consulta, headers=headers, json=payload, timeout=30)

                    if response.status_code == 200:
                        resultado = response.json()

                        # Extração direta da renda
                        renda_valor = extrair_renda_simples(resultado)

                        if renda_valor:
                            loggerBot.info(f"Renda obtida: R$ {renda_valor:.2f} para CPF {cpf}")
                            return renda_valor
                        else:
                            loggerBot.warning(f"Renda não encontrada para CPF {cpf}")
                            return None

                    elif response.status_code == 404:
                        loggerBot.warning(f"CPF {cpf} não encontrado")
                        return None

                    else:
                        loggerBot.error(f"Erro API {response.status_code} para CPF {cpf}")
                        return None

                except Exception as e:
                    loggerBot.error(f"Erro na consulta CPF {cpf}: {str(e)}")
                    return None

            # Função simplificada para extrair renda - OTIMIZADA
            def extrair_renda_simples(resultado):
                """
                Extração direta e otimizada da renda estimada
                """
                try:
                    # Verificação básica da estrutura
                    if not resultado or "produtos" not in resultado:
                        return None

                    produtos = resultado["produtos"]

                    # Lista de possíveis produtos de renda (ordem de prioridade)
                    produtos_renda = [
                        "renda-estimada-pf", "rendaEstimadaPf", "renda_estimada_pf",
                        "capacidade-pagamento", "capacidadePagamento"
                    ]

                    # Buscar produto de renda
                    dados_renda = None
                    for produto in produtos_renda:
                        if produto in produtos:
                            dados_renda = produtos[produto]
                            break

                    if not dados_renda:
                        return None

                    # Lista de possíveis campos de valor (ordem de prioridade)
                    campos_valor = [
                        "rendaEstimada", "renda_estimada", "valorRendaEstimada",
                        "valor", "valorRenda", "rendaMensal", "amount"
                    ]

                    # Buscar valor da renda
                    for campo in campos_valor:
                        if campo in dados_renda:
                            valor = dados_renda[campo]

                            # Processar valor encontrado
                            valor_final = processar_valor_simples(valor)
                            if valor_final and valor_final > 0:
                                return valor_final

                    return None

                except Exception:
                    return None

            # Função simplificada para processar valor
            def processar_valor_simples(valor):
                """
                Processamento direto do valor de renda
                """
                try:
                    # Se é um objeto, tentar extrair valor
                    if isinstance(valor, dict):
                        for chave in ["valor", "amount", "valorNumerico"]:
                            if chave in valor:
                                valor = valor[chave]
                                break

                    # Se é string, converter para número
                    if isinstance(valor, str):
                        # Remover caracteres não numéricos exceto ponto e vírgula
                        valor_limpo = re.sub(r'[^\d,.]', '', valor).replace(',', '.')
                        valor = float(valor_limpo)

                    # Retornar se é um número válido
                    return float(valor) if isinstance(valor, (int, float)) and valor > 0 else None

                except Exception:
                    return None

            # FUNÇÃO REMOVIDA: buscar_renda_recursiva
            # Não utilizamos busca recursiva para manter especificidade
            # Buscamos APENAS campos específicos de renda estimada PF

            # Função simplificada para criar registro
            def criar_registro_consulta_api(cpf, renda):
                """
                Cria arquivo JSON com dados da consulta - Versão otimizada
                """
                try:
                    consultas_dir = "C:\\RPA\\AtualizacaoCadastral\\consultas"
                    os.makedirs(consultas_dir, exist_ok=True)

                    registro_path = os.path.join(consultas_dir, f"[{cpf}].json")

                    # Dados essenciais apenas
                    dados = {
                        "cpf": cpf,
                        "renda_estimada": renda,
                        "data_consulta": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "fonte": "API_SERASA"
                    }

                    with open(registro_path, 'w', encoding='utf-8') as f:
                        json.dump(dados, f, ensure_ascii=False)

                    loggerBot.info(f"Registro criado para CPF {cpf}")

                except Exception as e:
                    loggerBot.error(f"Erro ao criar registro CPF {cpf}: {str(e)}")

            # Obter token de acesso uma única vez
            loggerBot.info("Obtendo token de acesso para API Serasa...")
            access_token = obter_token_acesso()

            if not access_token:
                loggerBot.error("Falha ao obter token de acesso. Interrompendo execução.")
                raise Exception("Não foi possível autenticar na API do Serasa")

            loggerBot.info("Configuração da API Serasa concluída com sucesso")
            loggerBot.info("Iniciando processamento de CPFs via API...")

            # While Activity
            # Displayname: While
            while True:
                # Sequence: Body

                # Custom Python Code Activity
                # Displayname: LÊ TABELA ASSOCIADOS NO BANCO DE DADOS
                # Primeiro, consultar a quantidade total de registros pendentes
                consulta_pendentes = "SELECT COUNT(*) FROM associados WHERE status = 1"
                cursor.execute(consulta_pendentes)
                total_pendentes = cursor.fetchone()[0]
                
                if total_pendentes == 0:
                     loggerBot.info("Não há mais CPFs pendentes para processamento")
                     break  # Sai do loop quando não há mais registros
                                
                loggerBot.info(f"Quantidade de CPFs pendentes: {total_pendentes}")
                
                # Agora sim, iniciar o processamento do próximo registro
                cursor.execute("START TRANSACTION")

                # Sequence: Try Structure

                # Try Activity
                # Displayname: Try
                try:
                    # Sequence: Body

                    # Custom Python Code Activity
                    # Displayname: CodePython
                    # Seleciona o primeiro registro com status = 1
                    ler = "SELECT * FROM associados WHERE status = 1 LIMIT 1"
                    cursor.execute(ler)
                    dataList = cursor.fetchall()

                    # Sequence: Conditional Structure

                    # If Activity
                    # Displayname: If Condition
                    if dataList:
                        # Sequence: Body

                        # Custom Python Code Activity
                        # Displayname: CodePython
                        registro = dataList[0]
                        id_registro = registro[0]  # ID na primeira coluna
                        cpf = registro[1]  # CPF na segunda coluna
                        
                        # Atualiza o status para 9
                        atualizar = f"UPDATE associados SET status = 9 WHERE id = {id_registro}"
                        cursor.execute(atualizar)
                                                
                        # Commit da transação
                        conexao.commit()
                                                
                        loggerBot.info(f"CPF {cpf} bloqueado para processamento")

                        # ForEach Activity
                        # Displayname: ForEach
                        for linha in dataList:
                            # Sequence: Body

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            loggerBot.info("Iniciando consulta do CPF: "+linha[1])

                            # ============================================================================
                            # CONSULTA VIA API SERASA (SUBSTITUINDO AUTOMAÇÃO WEB)
                            # ============================================================================

                            # Custom Python Code Activity
                            # Displayname: Consulta Renda via API Serasa
                            cpf_consulta = linha[1]

                            # Realizar consulta via API
                            renda_valor = consultar_renda_api(cpf_consulta, access_token)

                            # Verificar resultado da consulta
                            if renda_valor is not None:
                                loggerBot.info(f"Consulta API Serasa realizada com sucesso para CPF {cpf_consulta}")
                                loggerBot.info(f"Renda obtida via API: R$ {renda_valor:.2f}")

                                # Criar um "PDF virtual" ou registro da consulta para manter compatibilidade
                                # com o resto do sistema que espera um PDF
                                criar_registro_consulta_api(cpf_consulta, renda_valor)

                            else:
                                loggerBot.error(f"Erro ao consultar renda via API para CPF {cpf_consulta}")
                                # Continuar para o próximo CPF em caso de erro
                                continue

                            # Criar registro da consulta API (substitui o PDF)
                            criar_registro_consulta_api(cpf_consulta, renda_valor)

                            # Simular tempo de processamento (opcional, para não sobrecarregar a API)
                            time.sleep(1)

                            # Sequence: SEQUENCIA SISBR

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            loggerBot.info("Aguardando 5 segundos para chamar a aplicação Sisbr para frente...")

                            # Instance Deskop Bot Activity
                            # Displayname: DesktopBot
                            deskBot = DesktopBot()

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(5000)

                            # Custom Python Code Activity
                            # Displayname: Trazer SISBR para Frente
                            x, y = 1807, 64
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(3000)

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada Pesquisar do Sisbr
                            # Simula o clique diretamente nas coordenadas (229, 993)
                            x, y = 229, 993
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(3000)

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            loggerBot.error("Acessando o módulo Plataforma de Atendimento")

                            # Type Into Activity
                            # Displayname: Type_Into
                            deskBot.paste(text="PLATAFORMA DE ATENDIMENTO", wait=0)

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(2000)

                            # Find Activity
                            # Displayname: Click Menu Plat Atendimento
                            clickPlatAtend = deskBot.find("clickPlatAtend", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Click Activity
                            # Displayname: Click Menu Plat Atendimento
                            deskBot.click()

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(7000)

                            # Find Activity
                            # Displayname: CampoCPF
                            campoCPF = deskBot.find("campoCPF", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Sequence: Conditional Structure

                            # If Activity
                            # Displayname: If Condition
                            if campoCPF:
                                # Sequence: Body

                                # Custom Python Code Activity
                                # Displayname: CodePython
                                loggerBot.info("Módulo Plataforma de Atendimento acessado com sucesso!")


                            # Else Activity
                            # Displayname: Else
                            else:
                                # Sequence: Body

                                # Custom Python Code Activity
                                # Displayname: CodePython
                                loggerBot.error("Erro ao acessar o módulo Plataforma de Atendimento.")


                            # Click Activity
                            # Displayname: Click
                            deskBot.click()

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(1500)

                            # Click Activity
                            # Displayname: Click
                            deskBot.click()

                            # Type Into Activity
                            # Displayname: Type_Into
                            deskBot.paste(text=linha[1], wait=0)

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(2000)

                            # Find Activity
                            # Displayname: Botão Pesquisar
                            btPesquisar = deskBot.find("btPesq", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Click Activity
                            # Displayname: Click no botão Pesquisar
                            deskBot.click()

                            # Click Activity
                            # Displayname: Click no botão Pesquisar
                            deskBot.click()

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            loggerBot.error("Consultando o CPF "+linha[1]+" na Plataforma de Atendimento")

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(7000)

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada Área Limpa
                            x, y = 1458, 112
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(1000)

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada fechar Validacao Cadastral
                            # Simula o clique diretamente nas coordenadas (1330, 309)
                            x, y = 1330, 309
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(1000)

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada fechar Validacao Cadastral
                            # Simula o clique diretamente nas coordenadas (1330, 309)
                            x, y = 1330, 309
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(2000)

                            # Custom Python Code Activity
                            # Displayname: Clica em Fechar a Plataforma de Atendimento
                            # Coordenada Fechar Plataforma de Atendimento
                            x, y = 1896, 10
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            loggerBot.error("Fechando Plataforma de Atendimento para eliminar propagandas")

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(3000)

                            # Sequence: ABRE PLATAFORMA ATENDIMENTO NOVAMENTE

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada Pesquisar do Sisbr
                            # Simula o clique diretamente nas coordenadas (229, 993)
                            x, y = 229, 993
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(3000)

                            # Type Into Activity
                            # Displayname: Type_Into
                            deskBot.paste(text="PLATAFORMA DE ATENDIMENTO", wait=0)

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(2000)

                            # Find Activity
                            # Displayname: Click Menu Plat Atendimento
                            clickPlatAtend = deskBot.find("clickPlatAtend", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Click Activity
                            # Displayname: Click Menu Plat Atendimento
                            deskBot.click()

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            loggerBot.error("Abrindo o módulo Plataforma de Atendimento novamente")

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(7000)

                            # Find Activity
                            # Displayname: CampoCPF
                            campoCPF = deskBot.find("campoCPF", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Sequence: Conditional Structure

                            # If Activity
                            # Displayname: If Condition
                            if campoCPF:
                                # Sequence: Body

                                # Custom Python Code Activity
                                # Displayname: CodePython
                                loggerBot.info("Módulo Plataforma de Atendimento acessado com sucesso!")


                            # Else Activity
                            # Displayname: Else
                            else:
                                # Sequence: Body

                                # Custom Python Code Activity
                                # Displayname: CodePython
                                loggerBot.error("Erro ao acessar o módulo Plataforma de Atendimento.")


                            # Click Activity
                            # Displayname: Click
                            deskBot.click()

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(1500)

                            # Click Activity
                            # Displayname: Click
                            deskBot.click()

                            # Type Into Activity
                            # Displayname: Type_Into
                            deskBot.paste(text=linha[1], wait=0)

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(2000)

                            # Find Activity
                            # Displayname: Botão Pesquisar
                            btPesquisar = deskBot.find("btPesq", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Click Activity
                            # Displayname: Click no botão Pesquisar
                            deskBot.click()

                            # Click Activity
                            # Displayname: Click no botão Pesquisar
                            deskBot.click()

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(7000)

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            loggerBot.error("Consultando o CPF "+linha[1]+" na Plataforma de Atendimento")

                            # Sequence: FECHA AVALIACAO CADASTRAL NOVAMENTE

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada Área Limpa
                            x, y = 1458, 112
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(1000)

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada fechar Validacao Cadastral
                            # Simula o clique diretamente nas coordenadas (1330, 309)
                            x, y = 1330, 309
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(1000)

                            # Custom Python Code Activity
                            # Displayname: Click na coordenada fechar Validacao Cadastral
                            # Simula o clique diretamente nas coordenadas (1330, 309)
                            x, y = 1330, 309
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(3000)

                            # Custom Python Code Activity
                            # Displayname: Botao AVALIACAO FINANCEIRA
                            # Coordenada opção AVALIAÇÃO FINANCEIRA
                            x, y = 27, 277
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(2000)

                            # Find Activity
                            # Displayname: btRenda
                            btRenda = deskBot.find("btRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Sequence: Conditional Structure

                            # If Activity
                            # Displayname: If Condition
                            if btRenda:
                                # Sequence: Body

                                # Custom Python Code Activity
                                # Displayname: CodePython
                                loggerBot.info("CPF "+linha[1]+" consultado na Plataforma de Atendimento")


                            # Else Activity
                            # Displayname: Else
                            else:
                                # Sequence: Body

                                # Custom Python Code Activity
                                # Displayname: CodePython
                                loggerBot.error("Erro ao consultar CPF na Plataforma de Atendimento.")


                            # Click Activity
                            # Displayname: Click
                            deskBot.click()

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(5000)

                            # Custom Python Code Activity
                            # Displayname: Clica na Renda 1
                            # Coordenada Linha 1
                            x, y = 1675, 269
                            
                            # Movimenta o cursor para as coordenadas e realiza o clique
                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                            

                            # Find Activity
                            # Displayname: Renda Ponderada
                            rendaPonderada = deskBot.find("rendaPonderada3", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # If Activity
                            # Displayname: RENDA PONDERADA
                            if rendaPonderada:
                                # Sequence: Body

                                # Custom Python Code Activity
                                # Displayname: Clica em Fechar a Plataforma de Atendimento
                                # Coordenada Fechar Plataforma de Atendimento
                                x, y = 1896, 10
                                
                                # Movimenta o cursor para as coordenadas e realiza o clique
                                win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse

                                # Wait Activity
                                # Displayname: Wait
                                # webBot.wait(2000)  # Comentado - não mais necessário com API

                                # Custom Python Code Activity
                                # Displayname: Trazer CHROME para Frente
                                # Função para trazer a aplicação para frente
                                def bring_application_to_front():
                                    try:
                                        # Acessando a aplicação via AppActivate
                                        shell = win32com.client.Dispatch("WScript.Shell")
                                        shell.AppActivate("Google Chrome")  # Nome da janela da aplicação
                                        
                                        # Espera um pouco para garantir que a janela está ativa
                                        time.sleep(1)
                                
                                        # Obtém o identificador da janela pela classe ou título
                                        hwnd = win32gui.FindWindow(None, "Google Chrome")  # Substitua "Google Chrome" com o título correto da janela
                                        
                                        if hwnd:
                                            # Garante que a janela seja trazida para frente
                                            win32gui.ShowWindow(hwnd, 5)  # 5 é SW_SHOW (mostrar janela)
                                            win32gui.SetForegroundWindow(hwnd)  # Traz a janela para frente
                                            print("Aplicação trazida para frente com sucesso!")
                                        else:
                                            print("Aplicação não encontrada!")
                                        
                                        # Tentativa adicional para garantir o foco
                                        time.sleep(1)  # Atraso antes da tentativa adicional
                                        if hwnd:
                                            win32gui.SetForegroundWindow(hwnd)  # Nova tentativa de trazer para frente
                                            print("Segunda tentativa de trazer a aplicação para frente.")
                                
                                    except Exception as e:
                                        print(f"Erro ao trazer a aplicação para frente: {e}")
                                
                                # Chamada da função no ponto necessário do script
                                # bring_application_to_front()  # Comentado - não mais necessário com API

                                # Wait Activity
                                # Displayname: Wait
                                # webBot.wait(2000)  # Comentado - não mais necessário com API

                                # Find Element Activity
                                # Displayname: Botão Nova Consulta
                                # btn_novaConsulta = webBot.find_element(selector="/html/body/app-root/app-layout/app-menu/div/ul/li[1]/a/span[1]/seds-icon/mat-icon", by=By.XPATH, waiting_time=1000, ensure_visible=False, ensure_clickable=False)

                                # Click Activity
                                # Displayname: Click
                                # btn_novaConsulta.click()

                                # Wait Activity
                                # Displayname: Wait
                                deskBot.wait(4000)

                                # Sequence: REGISTRO CPF NÃO ATUALIZADO - RENDA PONDERADA

                                # Custom Python Code Activity
                                # Displayname: ATUALIZANDO STATUS PARA RENDA PONDERADA
                                # ALTERAR STATUS PARA RENDA PONDERADA (7) NA TABELA ASSOCIADOS
                                
                                data_atualizacao = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Data e hora atual
                                cpf_ponderada = linha[1]
                                
                                update_ponderada = f"UPDATE associados SET status = '7', data_atualizacao = '{data_atualizacao}' WHERE cpf = '{cpf_ponderada}'"
                                cursor.execute(update_ponderada)
                                conexao.commit()

                                # Custom Python Code Activity
                                # Displayname: Mensagem Log
                                loggerBot.info("CPF "+linha[1]+" não atualizado: Renda Ponderada!")

                                # Continue Activity
                                # Displayname: Continue
                                continue


                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(2000)

                            # Find Activity
                            # Displayname: Img botão Excluir Renda
                            btExcluirRenda = deskBot.find("btExcluirRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                            # Sequence: Conditional Structure

                            # If Activity
                            # Displayname: If Condition
                            if btExcluirRenda:
                                # Sequence: FLUXO EXCLUIR/ALTERAR RENDAS - ATÉ 4

                                # Custom Python Code Activity
                                # Displayname: CHAMA FUNÇÃO EXCLUIR RENDA
                                excluir_renda()

                                # Find Activity
                                # Displayname: ERRO INTERNO
                                erroInterno = deskBot.find("erroInterno", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)

                                # Sequence: Conditional Structure

                                # If Activity
                                # Displayname: If Condition
                                if erroInterno:
                                    # Sequence: IF RENDA 1

                                    # Custom Python Code Activity
                                    # Displayname: Mensagem Log
                                    loggerBot.error("Impossível realizar exclusão. Única renda cadastrada.")

                                    # Custom Python Code Activity
                                    # Displayname: Clica em OK ***VERIFICAR COORDENADAS***
                                    # Coordenada Clique em OK - Mensagem não é possível excluir
                                    x, y = 1082, 596
                                    
                                    # Movimenta o cursor para as coordenadas e realiza o clique
                                    win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                    

                                    # Wait Activity
                                    # Displayname: Wait
                                    deskBot.wait(3000)

                                    # Custom Python Code Activity
                                    # Displayname: Clica na Renda 1
                                    # Coordenada Linha 1
                                    x, y = 1675, 269
                                    
                                    # Movimenta o cursor para as coordenadas e realiza o clique
                                    win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                    

                                    # Wait Activity
                                    # Displayname: Wait
                                    deskBot.wait(2000)

                                    # Find Activity
                                    # Displayname: Botão Alterar Renda
                                    bt_alterarRenda = deskBot.find("bt_alterarRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                                    # Click Activity
                                    # Displayname: Click no botão Alterar
                                    deskBot.click()

                                    # Custom Python Code Activity
                                    # Displayname: CHAMA A FUNÇÃO ALTERAR RENDA
                                    valorRenda = atualizar_renda()


                                # Else Activity
                                # Displayname: Else
                                else:
                                    # Sequence: Body

                                    # Custom Python Code Activity
                                    # Displayname: Mensagem Log
                                    loggerBot.info("Renda excluída com sucesso!")

                                    # Custom Python Code Activity
                                    # Displayname: Clica na Renda 2
                                    # Coordenada Linha 2
                                    x, y = 1667, 292
                                    
                                    # Movimenta o cursor para as coordenadas e realiza o clique
                                    win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                    

                                    # Wait Activity
                                    # Displayname: Wait
                                    deskBot.wait(2000)

                                    # Custom Python Code Activity
                                    # Displayname: CHAMA FUNÇÃO EXCLUIR RENDA
                                    excluir_renda()

                                    # Find Activity
                                    # Displayname: Find
                                    erroInterno = deskBot.find("erroInterno", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)

                                    # Sequence: Conditional Structure

                                    # If Activity
                                    # Displayname: If Condition
                                    if erroInterno:
                                        # Sequence: IF RENDA 2

                                        # Custom Python Code Activity
                                        # Displayname: Mensagem Log
                                        loggerBot.error("Impossível realizar exclusão. Única renda cadastrada.")

                                        # Custom Python Code Activity
                                        # Displayname: Clica em OK ***VERIFICAR COORDENADAS***
                                        # Coordenada Clique em OK - Mensagem não é possível excluir
                                        x, y = 1082, 596
                                        
                                        # Movimenta o cursor para as coordenadas e realiza o clique
                                        win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                        

                                        # Wait Activity
                                        # Displayname: Wait
                                        deskBot.wait(3000)

                                        # Custom Python Code Activity
                                        # Displayname: Clica na Renda 2
                                        # Coordenada Linha 2
                                        x, y = 1667, 292
                                        
                                        # Movimenta o cursor para as coordenadas e realiza o clique
                                        win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                        

                                        # Wait Activity
                                        # Displayname: Wait
                                        deskBot.wait(2000)

                                        # Find Activity
                                        # Displayname: Botão Alterar Renda
                                        bt_alterarRenda = deskBot.find("bt_alterarRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                                        # Click Activity
                                        # Displayname: Click no botão Alterar
                                        deskBot.click()

                                        # Custom Python Code Activity
                                        # Displayname: CHAMA FUNÇÃO ATUALIZAR RENDA
                                        valorRenda = atualizar_renda()


                                    # Else Activity
                                    # Displayname: Else
                                    else:
                                        # Sequence: Body

                                        # Custom Python Code Activity
                                        # Displayname: Mensagem Log
                                        loggerBot.info("Renda excluída com sucesso!")

                                        # Custom Python Code Activity
                                        # Displayname: Clica na Renda 3
                                        # Coordenada Linha 3
                                        x, y = 1678, 315
                                        
                                        # Movimenta o cursor para as coordenadas e realiza o clique
                                        win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                        

                                        # Wait Activity
                                        # Displayname: Wait
                                        deskBot.wait(2000)

                                        # Custom Python Code Activity
                                        # Displayname: CHAMA FUNÇÃO EXCLUIR RENDA
                                        excluir_renda()

                                        # Find Activity
                                        # Displayname: Find
                                        erroInterno = deskBot.find("erroInterno", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)

                                        # Sequence: Conditional Structure

                                        # If Activity
                                        # Displayname: If Condition
                                        if erroInterno:
                                            # Sequence: IF RENDA 3

                                            # Custom Python Code Activity
                                            # Displayname: Mensagem Log
                                            loggerBot.error("Impossível realizar exclusão. Única renda cadastrada.")

                                            # Custom Python Code Activity
                                            # Displayname: Clica em OK ***VERIFICAR COORDENADAS***
                                            # Coordenada Clique em OK - Mensagem não é possível excluir
                                            x, y = 1082, 596
                                            
                                            # Movimenta o cursor para as coordenadas e realiza o clique
                                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                            

                                            # Wait Activity
                                            # Displayname: Wait
                                            deskBot.wait(3000)

                                            # Custom Python Code Activity
                                            # Displayname: Clica na Renda 3
                                            # Coordenada Linha 3
                                            x, y = 1678, 315
                                            
                                            # Movimenta o cursor para as coordenadas e realiza o clique
                                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                            

                                            # Wait Activity
                                            # Displayname: Wait
                                            deskBot.wait(2000)

                                            # Find Activity
                                            # Displayname: Botão Alterar Renda
                                            bt_alterarRenda = deskBot.find("bt_alterarRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                                            # Click Activity
                                            # Displayname: Click no botão Alterar
                                            deskBot.click()

                                            # Custom Python Code Activity
                                            # Displayname: CHAMA FUNÇÃO ATUALIZAR RENDA
                                            valorRenda = atualizar_renda()


                                        # Else Activity
                                        # Displayname: Else
                                        else:
                                            # Sequence: Body

                                            # Custom Python Code Activity
                                            # Displayname: Mensagem Log
                                            loggerBot.info("Renda excluída com sucesso!")

                                            # Custom Python Code Activity
                                            # Displayname: Clica na Renda 4
                                            # Coordenada Linha 4
                                            x, y = 1678, 338
                                            
                                            # Movimenta o cursor para as coordenadas e realiza o clique
                                            win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                            win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                            

                                            # Wait Activity
                                            # Displayname: Wait
                                            deskBot.wait(2000)

                                            # Custom Python Code Activity
                                            # Displayname: CHAMA FUNÇÃO EXCLUIR RENDA
                                            excluir_renda()

                                            # Find Activity
                                            # Displayname: Find
                                            erroInterno = deskBot.find("erroInterno", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)

                                            # Sequence: Conditional Structure

                                            # If Activity
                                            # Displayname: If Condition
                                            if erroInterno:
                                                # Sequence: Body

                                                # Sequence: IF RENDA 4

                                                # Custom Python Code Activity
                                                # Displayname: Mensagem Log
                                                loggerBot.error("Impossível realizar exclusão. Única renda cadastrada.")

                                                # Custom Python Code Activity
                                                # Displayname: Clica em OK ***VERIFICAR COORDENADAS***
                                                # Coordenada Clique em OK - Mensagem não é possível excluir
                                                x, y = 1082, 596
                                                
                                                # Movimenta o cursor para as coordenadas e realiza o clique
                                                win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                

                                                # Wait Activity
                                                # Displayname: Wait
                                                deskBot.wait(3000)

                                                # Custom Python Code Activity
                                                # Displayname: Clica na Renda 4
                                                # Coordenada Linha 4
                                                x, y = 1678, 338
                                                
                                                # Movimenta o cursor para as coordenadas e realiza o clique
                                                win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                

                                                # Wait Activity
                                                # Displayname: Wait
                                                deskBot.wait(2000)

                                                # Find Activity
                                                # Displayname: Botão Alterar Renda
                                                bt_alterarRenda = deskBot.find("bt_alterarRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                                                # Click Activity
                                                # Displayname: Click no botão Alterar
                                                deskBot.click()

                                                # Custom Python Code Activity
                                                # Displayname: CHAMA FUNÇÃO ATUALIZAR RENDA
                                                valorRenda = atualizar_renda()


                                            # Else Activity
                                            # Displayname: Else
                                            else:
                                                # Sequence: Body

                                                # Custom Python Code Activity
                                                # Displayname: Mensagem Log
                                                loggerBot.info("Renda excluída com sucesso!")

                                                # Custom Python Code Activity
                                                # Displayname: Clica na Renda 5 ** PEGAR COORDENADA **
                                                # Coordenada Linha 5
                                                x, y = 1678, 355
                                                
                                                # Movimenta o cursor para as coordenadas e realiza o clique
                                                win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                

                                                # Wait Activity
                                                # Displayname: Wait
                                                deskBot.wait(2000)

                                                # Custom Python Code Activity
                                                # Displayname: CHAMA FUNÇÃO EXCLUIR RENDA
                                                excluir_renda()

                                                # Find Activity
                                                # Displayname: Find
                                                erroInterno = deskBot.find("erroInterno", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)

                                                # Sequence: Conditional Structure

                                                # If Activity
                                                # Displayname: If Condition
                                                if erroInterno:
                                                    # Sequence: Body

                                                    # Sequence: IF RENDA 5

                                                    # Custom Python Code Activity
                                                    # Displayname: Mensagem Log
                                                    loggerBot.error("Impossível realizar exclusão. Única renda cadastrada.")

                                                    # Custom Python Code Activity
                                                    # Displayname: Clica em OK ***VERIFICAR COORDENADAS***
                                                    # Coordenada Clique em OK - Mensagem não é possível excluir
                                                    x, y = 1082, 596
                                                    
                                                    # Movimenta o cursor para as coordenadas e realiza o clique
                                                    win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                    

                                                    # Wait Activity
                                                    # Displayname: Wait
                                                    deskBot.wait(3000)

                                                    # Custom Python Code Activity
                                                    # Displayname: Clica na Renda 5
                                                    # Coordenada Linha 5
                                                    x, y = 1678, 355
                                                    
                                                    # Movimenta o cursor para as coordenadas e realiza o clique
                                                    win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                    

                                                    # Wait Activity
                                                    # Displayname: Wait
                                                    deskBot.wait(2000)

                                                    # Find Activity
                                                    # Displayname: Botão Alterar Renda
                                                    bt_alterarRenda = deskBot.find("bt_alterarRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                                                    # Click Activity
                                                    # Displayname: Click no botão Alterar
                                                    deskBot.click()

                                                    # Custom Python Code Activity
                                                    # Displayname: CHAMA FUNÇÃO ATUALIZAR RENDA
                                                    valorRenda = atualizar_renda()


                                                # Else Activity
                                                # Displayname: Else
                                                else:
                                                    # Sequence: Body

                                                    # Custom Python Code Activity
                                                    # Displayname: Mensagem Log
                                                    loggerBot.info("Renda excluída com sucesso!")

                                                    # Custom Python Code Activity
                                                    # Displayname: Clica na Renda 6
                                                    # Coordenada Linha 6
                                                    x, y = 1678, 377
                                                    
                                                    # Movimenta o cursor para as coordenadas e realiza o clique
                                                    win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                    

                                                    # Wait Activity
                                                    # Displayname: Wait
                                                    deskBot.wait(2000)

                                                    # Custom Python Code Activity
                                                    # Displayname: CHAMA FUNÇÃO EXCLUIR RENDA
                                                    excluir_renda()

                                                    # Find Activity
                                                    # Displayname: Find
                                                    erroInterno = deskBot.find("erroInterno", x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)

                                                    # Sequence: Conditional Structure

                                                    # If Activity
                                                    # Displayname: If Condition
                                                    if erroInterno:
                                                        # Sequence: IF RENDA 6

                                                        # Custom Python Code Activity
                                                        # Displayname: Mensagem Log
                                                        loggerBot.error("Impossível realizar exclusão. Única renda cadastrada.")

                                                        # Custom Python Code Activity
                                                        # Displayname: Clica em OK ***VERIFICAR COORDENADAS***
                                                        # Coordenada Clique em OK - Mensagem não é possível excluir
                                                        x, y = 1082, 596
                                                        
                                                        # Movimenta o cursor para as coordenadas e realiza o clique
                                                        win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                        

                                                        # Wait Activity
                                                        # Displayname: Wait
                                                        deskBot.wait(3000)

                                                        # Custom Python Code Activity
                                                        # Displayname: Clica na Renda 6
                                                        # Coordenada Linha 6
                                                        x, y = 1678, 377
                                                        
                                                        # Movimenta o cursor para as coordenadas e realiza o clique
                                                        win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                                        

                                                        # Wait Activity
                                                        # Displayname: Wait
                                                        deskBot.wait(2000)

                                                        # Find Activity
                                                        # Displayname: Botão Alterar Renda
                                                        bt_alterarRenda = deskBot.find("bt_alterarRenda", x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)

                                                        # Click Activity
                                                        # Displayname: Click no botão Alterar
                                                        deskBot.click()

                                                        # Custom Python Code Activity
                                                        # Displayname: CHAMA FUNÇÃO ATUALIZAR RENDA
                                                        valorRenda = atualizar_renda()








                            # Else Activity
                            # Displayname: Else
                            else:
                                # Sequence: NOVA RENDA

                                # Custom Python Code Activity
                                # Displayname: Mensagem Log
                                loggerBot.error("CPF "+linha[1]+" não possui renda cadastrada")

                                # Custom Python Code Activity
                                # Displayname: Mensagem Log
                                loggerBot.info("Iniciando cadastro da renda...")

                                # Custom Python Code Activity
                                # Displayname: Clica no botão Nova Renda
                                # Coordenada Nova Renda
                                x, y = 1888, 916
                                
                                # Movimenta o cursor para as coordenadas e realiza o clique
                                win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas
                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse
                                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse
                                

                                # Wait Activity
                                # Displayname: Wait
                                deskBot.wait(3000)

                                # Custom Python Code Activity
                                # Displayname: CHAMA FUNÇÃO NOVA RENDA
                                valorRenda = nova_renda()

                                # Wait Activity
                                # Displayname: Wait
                                deskBot.wait(5000)


                            # Sequence: REGISTRO DOS CPFs REALIZADOS

                            # Custom Python Code Activity
                            # Displayname: REGISTRA ATUALIZACAO NO BANCO DE DADOS
                            # Obter a data e hora atuais
                            data_atualizacao_final = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Data e hora atual
                            
                            cpf_update = linha[1]
                            
                            # Garantir que valorRenda seja um número float antes de qualquer operação
                            # Converter formato brasileiro (1.200,00) para formato americano (1200.00)
                            try:
                                if not valorRenda or valorRenda.strip() == "":
                                    raise ValueError("valorRenda está vazio ou nulo")
                                
                                # Remove pontos (separadores de milhares) e substitui vírgula por ponto
                                valorRenda_limpo = valorRenda.replace('.', '').replace(',', '.')
                                valorRenda_float = float(valorRenda_limpo)
                                loggerBot.info(f"Valor da renda convertido: {valorRenda} -> {valorRenda_float}")
                                
                            except (ValueError, AttributeError) as e:
                                error_msg = f"ERRO CRÍTICO: Falha ao converter valorRenda '{valorRenda}' para float: {e}"
                                loggerBot.error(error_msg)
                                print(error_msg)
                                # Re-lança o erro para interromper a execução
                                raise ValueError(f"Conversão de renda falhou para CPF {linha[1]}: {valorRenda} -> {e}")
                            
                            valorRenda_final = f"{valorRenda_float:.2f}"
                            
                            cpf_encaminhado = f"UPDATE associados SET status = '2', renda = '{valorRenda_final}', data_encaminhado = '{data_atualizacao_final}' WHERE cpf = '{cpf_update}'"
                            cursor.execute(cpf_encaminhado)
                            conexao.commit()

                            # Wait Activity
                            # Displayname: Wait
                            deskBot.wait(3000)

                            # Custom Python Code Activity
                            # Displayname: Mover PDF para a pasta concluidos
                            # Após registrar o CPF como concluido, mover o arquivo PDF para a pasta "concluidos"
                            try:
                                # Caminho do arquivo original na pasta "consultas"
                                arquivo_origem = os.path.join("C:\\RPA\\AtualizacaoCadastral\\consultas", f"[{linha[1]}].pdf")
                            
                                # Caminho para a pasta "concluidos"
                                arquivo_destino = os.path.join("C:\\RPA\\AtualizacaoCadastral\\concluidos", f"[{linha[1]}].pdf")
                            
                                # Verificar se o arquivo existe na pasta "consultas"
                                if os.path.exists(arquivo_origem):
                                    # Mover o arquivo para a pasta "concluidos"
                                    shutil.move(arquivo_origem, arquivo_destino)
                                    print(f"Arquivo movido para: {arquivo_destino}")
                                else:
                                    print(f"Arquivo não encontrado para mover: {arquivo_origem}")
                            
                            except Exception as e:
                                print(f"Erro ao mover o arquivo: {e}")

                            # Custom Python Code Activity
                            # Displayname: Mensagem Log
                            loggerBot.info("Atualização de renda para o CPF "+linha[1]+" encaminhada para autorização!")

                            # Custom Python Code Activity
                            # Displayname: Trazer CHROME para Frente
                            # Função para trazer a aplicação para frente
                            def bring_application_to_front():
                                try:
                                    # Acessando a aplicação via AppActivate
                                    shell = win32com.client.Dispatch("WScript.Shell")
                                    shell.AppActivate("Google Chrome")  # Nome da janela da aplicação
                                    
                                    # Espera um pouco para garantir que a janela está ativa
                                    time.sleep(1)
                            
                                    # Obtém o identificador da janela pela classe ou título
                                    hwnd = win32gui.FindWindow(None, "Google Chrome")  # Substitua "Google Chrome" com o título correto da janela
                                    
                                    if hwnd:
                                        # Garante que a janela seja trazida para frente
                                        win32gui.ShowWindow(hwnd, 5)  # 5 é SW_SHOW (mostrar janela)
                                        win32gui.SetForegroundWindow(hwnd)  # Traz a janela para frente
                                        print("Aplicação trazida para frente com sucesso!")
                                    else:
                                        print("Aplicação não encontrada!")
                                    
                                    # Tentativa adicional para garantir o foco
                                    time.sleep(1)  # Atraso antes da tentativa adicional
                                    if hwnd:
                                        win32gui.SetForegroundWindow(hwnd)  # Nova tentativa de trazer para frente
                                        print("Segunda tentativa de trazer a aplicação para frente.")
                            
                                except Exception as e:
                                    print(f"Erro ao trazer a aplicação para frente: {e}")
                            
                            # Chamada da função no ponto necessário do script
                            # bring_application_to_front()  # Comentado - não mais necessário com API

                            # Find Element Activity
                            # Displayname: Botão Nova Consulta
                            # btn_novaConsulta = webBot.find_element(selector="/html/body/app-root/app-layout/app-menu/div/ul/li[1]/a/span[1]/seds-icon/mat-icon", by=By.XPATH, waiting_time=1000, ensure_visible=False, ensure_clickable=False)

                            # Click Activity
                            # Displayname: Click
                            # btn_novaConsulta.click()

                            # Wait Activity
                            # Displayname: Wait
                            # webBot.wait(4000)  # Comentado - não mais necessário com API

                            # Custom Python Code Activity
                            # Displayname: CodePython
                            pass



                    # Else Activity
                    # Displayname: Else
                    else:
                        # Sequence: Body

                        # Custom Python Code Activity
                        # Displayname: CodePython
                        conexao.commit()
                        loggerBot.info("Nenhum registro disponível para processamento")
                        break  # Sai do loop se não encontrou registros



                except Exception as e:
                    # Sequence: Body

                    # Custom Python Code Activity
                    # Displayname: CodePython
                    # Em caso de erro, faz rollback
                    conexao.rollback()
                    loggerBot.error(f"Erro ao processar registro: {str(e)}")
                    raise e



            # Wait Activity
            # Displayname: Wait
            # webBot.wait(5000)  # Comentado - não mais necessário com API


        except Exception as ex:
            # Sequence: Body

            # Custom Python Code Activity
            # Displayname: Tira Screenshot e captura os logs
            loggerBot.error("Erro durante a execução do bot", exc_info=True)
            
            # Criar diretório para as screenshots, caso não exista
            screenshot_dir = "C:\\RPA\\AtualizacaoCadastral\\logs\\screenshots"
            if not os.path.exists(screenshot_dir):
                 os.makedirs(screenshot_dir)
                        
            # Nome do arquivo de screenshot com data e hora para garantir que seja único
            screenshot_filename = datetime.now().strftime("%Y-%m-%d_%H-%M-%S") + "_error.png"
            screenshot_path = os.path.join(screenshot_dir, screenshot_filename)
                        
            # Capturar a screenshot e salvar no diretório
            pyautogui.screenshot(screenshot_path)
            loggerBot.info(f"Screenshot salva em: {screenshot_path}")

            # Sequence: ALTERA STATUS DO CPF PARA NAO ENCAMINHADO NO BANCO DE DADOS

            # Custom Python Code Activity
            # Displayname: CodePython
            if 'linha' in locals() and linha:
                 loggerBot.error("CPF "+linha[1]+" não encaminhado para atualização.")
            
                 # Atualizar status para 4
                 data_atualizacao_final2 = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                 cpf_update2 = linha[1]
                                    
                 cpf_naoencaminhado = f"UPDATE associados SET status = '4', data_atualizacao = '{data_atualizacao_final2}' WHERE cpf = '{cpf_update2}'"
                 cursor.execute(cpf_naoencaminhado)
                 conexao.commit()


        except Exception as ex:
            # Sequence: Body

            # Custom Python Code Activity
            # Displayname: CodePython
             # Tratamento de erro global
            loggerBot.error("Erro crítico durante a execução do bot", exc_info=True)
            raise ex


        finally :
            # Sequence: Body

            # Custom Python Code Activity
            # Displayname: CodePython
            # Encerrar os handlers para liberar o arquivo
            for handler in loggerBot.handlers:
                 handler.close()
                 loggerBot.removeHandler(handler)



        return
if __name__ == '__main__':
    bot = Bot()
    bot.bot()