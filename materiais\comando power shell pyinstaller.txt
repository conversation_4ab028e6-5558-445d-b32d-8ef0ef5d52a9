pyinstaller --onefile --clean `
  --add-data "resources\*;resources" `
  --add-data "AtualizacaoCadastral.jproj;." `
  --add-data ".env;." `
  --add-data "requirements.txt;." `
  --add-data "requirements.bat;." `
  --add-data "install_requirements.py;." `
  --add-data "concluidos\*;concluidos" `
  --add-data "consultas\*;consultas" `
  --add-data "logs\*;logs" `
  --add-data "Bot.xaml;." `
  --add-data "drivers\*;drivers" `
  --add-data "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\mysql\connector\locales;mysql\connector\locales" `
  --add-data "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\mysql\connector\plugins;mysql\connector\plugins" `
  bot.py