Python Code depois do Read Excel

# ====================================
# Código adicional para manipulação do PDF:
# ====================================
        
# Configurações do Chrome para download automático
download_dir = "C:\\RPA\\AtualizacaoCadastral\\"
if not os.path.exists(download_dir):
     os.makedirs(download_dir)

options = {
     "download.default_directory": download_dir,  # Diretório de download
     "download.prompt_for_download": False,        # Desativa o prompt de download
     "plugins.always_open_pdf_externally": True,    # Abre PDFs automaticamente sem a visualização
     "printing.print_preview_sticky_settings": False, # Desativa a pré-visualização de impressão
     "savefile.default_directory": download_dir,    # Define o diretório de salvamento
     "printing.print_to_pdf": True,  # Define para imprimir em PDF
     "savefile.filename": "",       # Deixa o nome do arquivo em branco (não permite sobrescrever)
     "print.print_backgrounds": True, # Imprimir fundos
     "print.print_headers_footers": False  # Desativa cabeçalhos e rodapés
}
webBot.driver_options = options


Python Code dentro do for

# O nome do arquivo PDF será baseado no CPF da linha
file_name = f"{linha[0]}.pdf"  # Usando o CPF como nome do arquivo
file_path = os.path.join(download_dir, file_name)

# Log para verificar onde o arquivo está sendo baixado
print(f"Esperando o download do arquivo {file_name} em {file_path}")


# Espera o arquivo ser baixado (com um tempo máximo de espera)
max_wait_time = 30  # Limite de 30 segundos
wait_time = 0
while not os.path.exists(file_path) and wait_time < max_wait_time:
     webBot.wait(1)  # Espera 1 segundo
     wait_time += 1

# Mover o arquivo para a pasta de destino, caso tenha um nome diferente
if os.path.exists(file_path):
     print(f"Arquivo {file_name} encontrado.")
     final_path = os.path.join(download_dir, file_name)
     os.rename(file_path, final_path)
     print(f"Arquivo salvo como: {final_path}")
else:
     print(f"Erro: O arquivo {file_name} não foi encontrado após {max_wait_time} segundos.")