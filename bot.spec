# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['bot.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources\\*', 'resources'),
        ('AtualizacaoCadastral.jproj', '.'),
        ('.env', '.'),
        ('requirements.txt', '.'),
        ('requirements.bat', '.'),
        ('install_requirements.py', '.'),
        ('concluidos\\*', 'concluidos'),
        ('consultas\\*', 'consultas'),
        ('logs\\*', 'logs'),
        ('Bot.xaml', '.'),
        ('drivers\\*', 'drivers'),
        # Inclusão dos diretórios do mysql.connector:
        ('C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Lib/site-packages/mysql/connector/locales/*', 'mysql/connector/locales'),
        ('C:/Users/<USER>/AppData/Local/Programs/Python/Python312/Lib/site-packages/mysql/connector/plugins/*', 'mysql/connector/plugins'),
    ],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)