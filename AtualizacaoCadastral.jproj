{"NameProject": "AtualizacaoCadastral", "Main": "Bot.xaml", "Description": null, "Tecnology": "Python", "Dependencies": [{"Import": "import time"}, {"Import": "import win32api"}, {"Import": "import win32con"}, {"Import": "import win32gui"}, {"Import": "from openpyxl import load_workbook"}, {"Import": "import pyautogui"}, {"Import": "import psutil"}, {"Import": "import mysql.connector"}], "WebDrivers": [{"Import": "CUSTOM,C:\\RPA\\WebDrivers\\custom.exe"}, {"Import": "EDGE,C:\\RPA\\WebDrivers\\edge.exe"}, {"Import": "FIREFOX,C:\\RPA\\WebDrivers\\firefox.exe"}, {"Import": "CHROME,C:\\RPA\\AtualizacaoCadastral\\Drivers\\chromedriver-win64\\chromedriver.exe"}], "MaestroLogin": null, "Packages": [{"Import": "JornadaRPA.Web"}, {"Import": "JornadaRPA.Desktop"}, {"Import": "JornadaRPA.Mouse"}, {"Import": "JornadaRPA.Python"}, {"Import": "JornadaRPA.Excel"}, {"Import": "JornadaRPA.Coordinates"}, {"Import": "JornadaRPA.FilesAndFolders"}, {"Import": "JornadaRPA.FileHandling"}, {"Import": "JornadaRPA.Image"}, {"Import": "JornadaRPA.Keyboard"}, {"Import": "os"}, {"Import": "dotenv"}, {"Import": "JornadaRPA.DotENV"}, {"Import": "os"}, {"Import": "pytesseract"}, {"Import": "Pillow"}, {"Import": "cv2"}, {"Import": "re"}, {"Import": "watchdog"}, {"Import": "JornadaRPA.PDF"}, {"Import": "JornadaRPA.win32com"}, {"Import": "JornadaRPA<PERSON>Logger"}], "PathSelector": "C:\\Jornada RPA Selector 1.0\\JornadaRPA.Selector.exe", "PathRunner": "C:\\"}