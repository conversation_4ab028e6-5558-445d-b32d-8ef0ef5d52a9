# Documentação das Mudanças - Integração API Serasa

## Resumo das Alterações

Este documento descreve as mudanças realizadas no RPA para substituir a automação web do portal Serasa por consultas via API REST.

## Principais Modificações

### 1. Arquivo `.env` - Novas Credenciais
```env
# Credenciais da API Serasa (NOVAS)
SERASA_API_TOKEN = "seu_token_api_aqui"
SERASA_CLIENT_ID = "seu_client_id_aqui"
SERASA_CLIENT_SECRET = "seu_client_secret_aqui"
```

### 2. Arquivo `requirements.txt` - Novas Dependências
```
requests==2.31.0   # Adicionada para chamadas HTTP à API
reportlab==4.0.4   # Adicionada para criação de PDFs
```

### 3. <PERSON><PERSON><PERSON> (`bot.py`)

#### 3.1 Sequência Original Comentada (Linhas 1028-1228)
- Todo o código de automação web foi comentado como backup
- Inclui login no portal, navegação e download de PDFs

#### 3.2 Nova Implementação API (Linhas 1229-1492)

**Funções Principais:**

1. **`obter_token_acesso()`**
   - Autentica via OAuth2 na API Serasa
   - Retorna token de acesso para consultas

2. **`consultar_renda_api(cpf, access_token)`**
   - Realiza consulta EXCLUSIVA de renda estimada PF via API REST
   - Endpoint: `/v1/relatorio-avancado-pf`
   - Produto: `renda-estimada-pf` (ESPECÍFICO para Pessoa Física)
   - Validação restritiva: APENAS renda estimada, nenhum outro dado

3. **`validar_resposta_renda_pf(resultado)`**
   - Valida que a resposta contém APENAS renda estimada PF
   - Rejeita qualquer outro tipo de consulta ou produto
   - Garante conformidade com requisitos específicos

4. **`extrair_renda_do_resultado(resultado)`**
   - Extrai EXCLUSIVAMENTE valor de renda estimada PF
   - Busca apenas campos específicos de renda PF
   - Não utiliza busca recursiva para manter especificidade

5. **`processar_valor_renda(valor_renda)`**
   - Processa e valida valores extraídos da API
   - Converte diferentes formatos para float
   - Validação rigorosa de valores numéricos

5. **`criar_registro_consulta_api(cpf, renda)`**
   - Cria arquivo JSON com dados da consulta
   - Substitui o PDF para manter compatibilidade

#### 3.3 Processamento de Renda Modificado (Linhas 247-304 e 653-710)
- Substituído processamento de PDF por leitura de dados da API
- Mantém formatação brasileira (1.234,56)
- Aplica limite máximo de R$ 10.000,00

#### 3.4 Referências WebBot Comentadas
- `webBot.wait()` - Comentadas onde não são mais necessárias
- `bring_application_to_front()` - Comentada
- Navegação entre páginas - Comentada

## Fluxo da Nova Implementação

### 1. Inicialização
```
1. Carrega credenciais da API do .env
2. Obtém token OAuth2
3. Valida autenticação
```

### 2. Loop Principal
```
Para cada CPF pendente:
1. Consulta EXCLUSIVAMENTE renda estimada PF via API REST
2. Valida que resposta contém APENAS renda estimada PF
3. Extrai valor específico da resposta JSON
4. Cria arquivo JSON de registro
5. Processa no Sisbr (inalterado)
6. Atualiza status no banco
```

### 3. Tratamento de Dados
```
1. Valor obtido da API
2. Aplicação de limite (R$ 10.000,00)
3. Formatação brasileira
4. Inserção no sistema Sisbr
```

## Configuração Necessária

### 1. Credenciais da API Serasa
Você precisa obter junto ao Serasa:
- **Client ID**: Identificador da aplicação
- **Client Secret**: Chave secreta da aplicação
- **Scopes**: `relatorio-avancado-pf`

### 2. Atualização do .env
```env
SERASA_CLIENT_ID = "seu_client_id_real"
SERASA_CLIENT_SECRET = "sua_chave_secreta_real"
```

### 3. Instalação de Dependências
```bash
pip install requests==2.31.0
```

## Vantagens da Nova Implementação

### 1. Performance
- **Velocidade**: Consultas instantâneas vs. navegação web
- **Confiabilidade**: Sem dependência de interface gráfica
- **Escalabilidade**: Múltiplas consultas simultâneas possíveis

### 2. Manutenibilidade
- **Estabilidade**: APIs são mais estáveis que interfaces web
- **Versionamento**: APIs têm controle de versão
- **Debugging**: Logs mais claros e estruturados

### 3. Robustez
- **Timeouts**: Controle preciso de timeouts
- **Retry**: Implementação de retry automático
- **Error Handling**: Tratamento específico por tipo de erro

## Compatibilidade

### 1. Sistema Sisbr
- **Inalterado**: Todo processamento no Sisbr permanece igual
- **Dados**: Mesma estrutura de dados inserida
- **Fluxo**: Mesmo fluxo de aprovação

### 2. Banco de Dados
- **Estados**: Mesmos status de controle
- **Estrutura**: Nenhuma alteração na tabela
- **Logs**: Mesma estrutura de logging

### 3. Arquivos
- **JSON**: Substitui PDFs para registro
- **Logs**: Mantém mesmo formato
- **Estrutura**: Mesmas pastas e organização

## Rollback (Volta ao Sistema Anterior)

Se necessário voltar ao sistema web:

1. **Descomente** as linhas 1028-1228 (sequência original)
2. **Comente** as linhas 1229-1492 (nova implementação API)
3. **Restaure** as chamadas webBot comentadas
4. **Remova** as modificações de processamento de renda

## Monitoramento e Logs

### 1. Logs da API
```
- "Obtendo token de acesso para API Serasa..."
- "Consulta API realizada com sucesso para CPF: {cpf}"
- "Renda extraída da API: R$ {valor}"
- "Registro de consulta API criado: {arquivo}"
```

### 2. Tratamento de Erros
```
- Timeout na consulta API
- Erro de autenticação
- CPF não encontrado (404)
- Erro na extração de renda
```

## Próximos Passos

1. **Teste** com CPFs de desenvolvimento
2. **Configure** credenciais reais da API
3. **Monitore** logs durante execução
4. **Ajuste** timeouts se necessário
5. **Documente** quaisquer problemas encontrados

## Suporte

Para dúvidas sobre a implementação:
- Verifique logs detalhados em `logs/`
- Consulte documentação oficial da API Serasa
- Teste com ferramentas como Postman antes da integração
