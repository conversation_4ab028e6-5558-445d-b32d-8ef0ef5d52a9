Trabalho no setor de TI de uma cooperativa de crédito.
Realizei o desenvolvimento de um RPA (em python) para atender uma demanda que me foi solicitada.

Preciso que crie toda a documentação formal e detalhada, com tópicos separando cada passo do RPA.

O que nos foi pedido?

Criar um RPA para realizar a atualização de renda de associados com base na consulta de renda do Serasa.

Requisitos: 
Associado Pessoa Física;
Renda de até R$10.000,00
Score acima de 500

A base de associados será gerada pelo setor de cadastro da cooperativa, filtrada de acordo com os requisitos acima, para que o RPA realize o processo solicitado.

Objetivo: Automatizar o processo de acordo com os requisitos, visando ganho significativo de tempo e mão de obra. Além disso, também resultará na redução do provisionamento, reclassificando os associados, reduzindo as despesas e consequentemente aumentando a receita da cooperativa.

Deve-se ressaltar na documentação também, que o RPA foi desenvolvido em um ambiente utilizando uma resolução de 1920x1080, e como ele utiliza coordenadas em vários de seus processos, é relevante que ele seja executado em um ambiente com estas configurações.

Deve-se ressaltar na documentação também, que a planilha base deve manter a estrutura, para que não atrapalhe o desenvolvimento do RPA.

Segue script em anexo, para análise e criação da documentação conforme solicitado acima.

