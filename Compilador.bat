@echo off
setlocal enabledelayedexpansion

REM Solicita a versão ao usuário
set /p APPVER=Digite a nova versão (ex: 1.20): 

REM Solicita a data/hora e descrição ao usuário
set /p APPDATA=Digite a data e descrição (ex: 10/07/2025 - 09:00 - Nova Funcionalidade): 

REM Atualiza o instalador2.iss com a nova versão
echo Atualizando instalador2.iss...

REM Atualiza a linha da versão
(for /f "usebackq delims=" %%A in ("instalador2.iss") do (
    set "line=%%A"
    echo !line! | findstr /C:"#define MyAppVersion" >nul
    if !errorlevel! == 0 (
        echo #define MyAppVersion "!APPVER!"
    ) else (
        echo !line!
    )
)) > instalador2_temp.iss

REM Atualiza a linha da data/descrição
(for /f "usebackq delims=" %%A in ("instalador2_temp.iss") do (
    set "line=%%A"
    echo !line! | findstr /C:"; Data de Atualização:" >nul
    if !errorlevel! == 0 (
        echo ; Data de Atualização: !APPDATA!
    ) else (
        echo !line!
    )
)) > instalador2.iss

del instalador2_temp.iss

REM Compila o executável com PyInstaller usando o arquivo .spec
echo Compilando o executável...
pyinstaller bot.spec

REM Verifica se a compilação foi bem-sucedida
if not exist dist\bot.exe (
    echo ERRO: O executável não foi gerado!
    pause
    exit /b 1
)

REM Gera o instalador com o Inno Setup
echo Gerando o instalador...
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" instalador2.iss

REM Verifica se o instalador foi gerado
if exist AtualizacaoCadastral_Installer_*.exe (
    echo Instalador gerado com sucesso!
) else (
    echo ERRO: O instalador não foi gerado!
)

pause