import subprocess
import sys
import os

def install_from_requirements():
    """Instala pacotes listados no arquivo requirements.txt."""
    # Verifica se o arquivo requirements.txt existe
    if os.path.exists('requirements.txt'):
        try:
            # Comando pip para instalar os pacotes do requirements.txt
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("Todos os pacotes do requirements.txt foram instalados com sucesso.")
        except subprocess.CalledProcessError as e:
            print(f"Erro ao tentar instalar os pacotes do requirements.txt: {e}")
    else:
        print("Arquivo requirements.txt não encontrado. Certifique-se de que o arquivo está no diretório atual.")

def install_tesseract():
    """Instrução para instalação manual do Tesseract OCR."""
    try:
        tesseract_url = "https://github.com/UB-Mannheim/tesseract/wiki"
        print(f"Por favor, baixe e instale o Tesseract OCR a partir do seguinte link: {tesseract_url}")
        print("Após a instalação, adicione o diretório de instalação ao PATH ou configure a variável de ambiente TESSDATA_PREFIX.")
    except Exception as e:
        print(f"Erro ao tentar configurar o Tesseract: {e}")

def install_requirements():
    """Instala os pacotes do requirements.txt e orientações para o Tesseract."""
    # Instala os pacotes a partir do requirements.txt
    install_from_requirements()

    # Instrução para instalar o Tesseract
    install_tesseract()

if __name__ == "__main__":
    install_requirements()
