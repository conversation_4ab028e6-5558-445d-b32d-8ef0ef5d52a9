#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste da API Serasa - Script de Validação
==========================================

Este script testa a integração com a API do Serasa Experian
para consulta de renda estimada antes da implementação no RPA principal.

Uso:
    python teste_api_serasa.py

Requisitos:
    - Credenciais válidas da API Serasa no arquivo .env
    - Biblioteca requests instalada
"""

import os
import requests
import json
from datetime import datetime
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Configurações da API Serasa
SERASA_API_BASE_URL = "https://api.serasaexperian.com.br"
SERASA_CLIENT_ID = os.getenv("SERASA_CLIENT_ID")
SERASA_CLIENT_SECRET = os.getenv("SERASA_CLIENT_SECRET")

def obter_token_acesso():
    """
    Obtém token de acesso OAuth2 para a API do Serasa
    """
    try:
        url_token = f"{SERASA_API_BASE_URL}/oauth/token"
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        data = {
            "grant_type": "client_credentials",
            "client_id": SERASA_CLIENT_ID,
            "client_secret": SERASA_CLIENT_SECRET,
            "scope": "relatorio-avancado-pf"
        }
        
        print("🔐 Obtendo token de acesso...")
        response = requests.post(url_token, headers=headers, data=data, timeout=30)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print("✅ Token obtido com sucesso!")
            return access_token
        else:
            print(f"❌ Erro ao obter token: {response.status_code}")
            print(f"Resposta: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erro na autenticação: {str(e)}")
        return None

def consultar_renda_api(cpf, access_token):
    """
    Consulta renda estimada via API do Serasa
    """
    try:
        url_consulta = f"{SERASA_API_BASE_URL}/v1/relatorio-avancado-pf"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        payload = {
            "documento": cpf,
            "tipoDocumento": "CPF",
            "produtos": [
                "capacidade-pagamento"
            ],
            "incluirScore": False,
            "incluirAlerta": False
        }
        
        print(f"🔍 Consultando CPF: {cpf}")
        response = requests.post(url_consulta, headers=headers, json=payload, timeout=60)
        
        if response.status_code == 200:
            resultado = response.json()
            print("✅ Consulta realizada com sucesso!")
            return resultado
            
        elif response.status_code == 404:
            print(f"⚠️  CPF {cpf} não encontrado na base Serasa")
            return None
            
        else:
            print(f"❌ Erro na consulta: {response.status_code}")
            print(f"Resposta: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print(f"⏰ Timeout na consulta para CPF: {cpf}")
        return None
        
    except Exception as e:
        print(f"❌ Erro na consulta: {str(e)}")
        return None

def extrair_renda_do_resultado(resultado):
    """
    Extrai o valor da renda estimada do resultado da API
    """
    try:
        print("🔍 Analisando estrutura da resposta...")
        
        # Salvar resposta completa para análise
        with open("resposta_api_completa.json", "w", encoding="utf-8") as f:
            json.dump(resultado, f, indent=2, ensure_ascii=False)
        print("📄 Resposta completa salva em 'resposta_api_completa.json'")
        
        # Buscar renda na estrutura esperada
        if "produtos" in resultado:
            produtos = resultado["produtos"]
            
            if "capacidade-pagamento" in produtos:
                capacidade = produtos["capacidade-pagamento"]
                print(f"📊 Dados de capacidade encontrados: {capacidade}")
                
                # Verificar diferentes campos possíveis
                campos_renda = [
                    "rendaEstimada",
                    "renda_estimada", 
                    "rendaEstimadaValor",
                    "valorRendaEstimada",
                    "capacidadePagamento",
                    "valor"
                ]
                
                for campo in campos_renda:
                    if campo in capacidade:
                        valor_renda = capacidade[campo]
                        print(f"💰 Campo '{campo}' encontrado: {valor_renda}")
                        
                        # Processar valor
                        if isinstance(valor_renda, dict):
                            if "valor" in valor_renda:
                                valor_renda = valor_renda["valor"]
                            elif "amount" in valor_renda:
                                valor_renda = valor_renda["amount"]
                        
                        if isinstance(valor_renda, (int, float)) and valor_renda > 0:
                            print(f"✅ Renda extraída: R$ {valor_renda:.2f}")
                            return valor_renda
        
        print("⚠️  Não foi possível extrair renda da resposta")
        return None
        
    except Exception as e:
        print(f"❌ Erro ao extrair renda: {str(e)}")
        return None

def main():
    """
    Função principal de teste
    """
    print("🚀 Iniciando teste da API Serasa")
    print("=" * 50)
    
    # Verificar credenciais
    if not SERASA_CLIENT_ID or not SERASA_CLIENT_SECRET:
        print("❌ Credenciais não encontradas no arquivo .env")
        print("Configure SERASA_CLIENT_ID e SERASA_CLIENT_SECRET")
        return
    
    print(f"🔑 Client ID: {SERASA_CLIENT_ID[:10]}...")
    
    # Obter token
    access_token = obter_token_acesso()
    if not access_token:
        print("❌ Falha na autenticação. Verifique as credenciais.")
        return
    
    # CPF de teste (substitua por um CPF válido para teste)
    cpf_teste = "12345678901"  # SUBSTITUA POR UM CPF REAL PARA TESTE
    
    print(f"\n📋 Testando com CPF: {cpf_teste}")
    print("⚠️  ATENÇÃO: Substitua por um CPF real para teste válido")
    
    # Realizar consulta
    resultado = consultar_renda_api(cpf_teste, access_token)
    
    if resultado:
        print("\n📊 Resultado da consulta:")
        print(json.dumps(resultado, indent=2, ensure_ascii=False)[:500] + "...")
        
        # Extrair renda
        renda = extrair_renda_do_resultado(resultado)
        
        if renda:
            # Aplicar limite e formatação
            renda_limitada = min(renda, 10000.00)
            renda_formatada = f"{renda_limitada:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
            
            print(f"\n💰 Resultado Final:")
            print(f"   Renda bruta: R$ {renda:.2f}")
            print(f"   Renda limitada: R$ {renda_limitada:.2f}")
            print(f"   Renda formatada: {renda_formatada}")
            
            print("\n✅ Teste concluído com sucesso!")
        else:
            print("\n❌ Falha na extração da renda")
    else:
        print("\n❌ Falha na consulta")
    
    print("\n" + "=" * 50)
    print("🏁 Teste finalizado")

if __name__ == "__main__":
    main()
