#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Teste da API Serasa - Script de Validação
==========================================

Este script testa a integração com a API do Serasa Experian
para consulta de renda estimada antes da implementação no RPA principal.

Uso:
    python teste_api_serasa.py

Requisitos:
    - Credenciais válidas da API Serasa no arquivo .env
    - Biblioteca requests instalada
"""

import os
import requests
import json
from datetime import datetime
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Configurações da API Serasa
SERASA_API_BASE_URL = "https://api.serasaexperian.com.br"
SERASA_CLIENT_ID = os.getenv("SERASA_CLIENT_ID")
SERASA_CLIENT_SECRET = os.getenv("SERASA_CLIENT_SECRET")

def obter_token_acesso():
    """
    Obtém token de acesso OAuth2 para a API do Serasa
    """
    try:
        url_token = f"{SERASA_API_BASE_URL}/oauth/token"
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        data = {
            "grant_type": "client_credentials",
            "client_id": SERASA_CLIENT_ID,
            "client_secret": SERASA_CLIENT_SECRET,
            "scope": "relatorio-avancado-pf"
        }
        
        print("🔐 Obtendo token de acesso...")
        response = requests.post(url_token, headers=headers, data=data, timeout=30)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print("✅ Token obtido com sucesso!")
            return access_token
        else:
            print(f"❌ Erro ao obter token: {response.status_code}")
            print(f"Resposta: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erro na autenticação: {str(e)}")
        return None

def consultar_renda_api(cpf, access_token):
    """
    Consulta EXCLUSIVAMENTE renda estimada para Pessoa Física via API do Serasa
    ATENÇÃO: Esta função busca APENAS renda estimada PF - nenhum outro dado
    """
    try:
        url_consulta = f"{SERASA_API_BASE_URL}/v1/relatorio-avancado-pf"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # Payload RESTRITIVO - APENAS renda estimada para Pessoa Física
        payload = {
            "documento": cpf,
            "tipoDocumento": "CPF",  # Garantir que é CPF (Pessoa Física)
            "produtos": [
                "renda-estimada-pf"  # PRODUTO ESPECÍFICO: Renda Estimada PF
            ],
            "incluirScore": False,      # NÃO incluir score
            "incluirAlerta": False,     # NÃO incluir alertas
            "incluirHistorico": False,  # NÃO incluir histórico
            "incluirRelacionamentos": False,  # NÃO incluir relacionamentos
            "incluirPatrimonio": False, # NÃO incluir patrimônio
            "incluirRendaComplementar": False,  # NÃO incluir renda complementar
            "tipoPessoa": "FISICA"      # EXPLICITAMENTE Pessoa Física
        }

        print(f"🔍 Consultando EXCLUSIVAMENTE renda estimada PF para CPF: {cpf}")
        print("📋 Produto solicitado: renda-estimada-pf (APENAS renda estimada)")

        response = requests.post(url_consulta, headers=headers, json=payload, timeout=60)

        if response.status_code == 200:
            resultado = response.json()
            print("✅ Consulta de renda estimada PF realizada com sucesso!")

            # Validar que recebemos APENAS dados de renda estimada
            if validar_resposta_renda_pf(resultado):
                return resultado
            else:
                print("❌ Resposta não contém APENAS renda estimada PF")
                return None

        elif response.status_code == 404:
            print(f"⚠️  CPF {cpf} não encontrado na base Serasa para renda estimada PF")
            return None

        else:
            print(f"❌ Erro na consulta de renda estimada PF: {response.status_code}")
            print(f"Resposta: {response.text}")
            return None

    except requests.exceptions.Timeout:
        print(f"⏰ Timeout na consulta de renda estimada PF para CPF: {cpf}")
        return None

    except Exception as e:
        print(f"❌ Erro na consulta de renda estimada PF: {str(e)}")
        return None

def validar_resposta_renda_pf(resultado):
    """
    Valida que a resposta contém APENAS dados de renda estimada para Pessoa Física
    """
    try:
        if not isinstance(resultado, dict):
            print("❌ Resposta não é um objeto JSON válido")
            return False

        if "produtos" not in resultado:
            print("❌ Resposta não contém seção 'produtos'")
            return False

        produtos = resultado["produtos"]

        # Verificar se contém APENAS renda estimada PF
        produtos_esperados = ["renda-estimada-pf", "rendaEstimadaPf", "renda_estimada_pf"]
        produto_encontrado = False

        for produto_esperado in produtos_esperados:
            if produto_esperado in produtos:
                produto_encontrado = True
                print(f"✅ Produto de renda estimada PF encontrado: {produto_esperado}")
                break

        if not produto_encontrado:
            print("❌ Resposta não contém produto de renda estimada PF")
            print(f"Produtos disponíveis: {list(produtos.keys())}")
            return False

        # Verificar se NÃO contém outros produtos indesejados
        produtos_proibidos = [
            "score", "credito", "historico", "relacionamentos",
            "patrimonio", "pj", "juridica", "empresarial",
            "negativacao", "protesto", "cheque", "spc", "serasa"
        ]

        for produto_key in produtos.keys():
            produto_lower = produto_key.lower()
            for proibido in produtos_proibidos:
                if proibido in produto_lower:
                    print(f"⚠️  Produto indesejado encontrado: {produto_key}")

        print("✅ Validação: APROVADA - contém apenas renda estimada PF")
        return True

    except Exception as e:
        print(f"❌ Erro na validação: {str(e)}")
        return False

def extrair_renda_do_resultado(resultado):
    """
    Extrai EXCLUSIVAMENTE o valor da renda estimada PF do resultado da API
    """
    try:
        print("🔍 Analisando estrutura da resposta para renda estimada PF...")

        # Salvar resposta completa para análise
        with open("resposta_api_completa.json", "w", encoding="utf-8") as f:
            json.dump(resultado, f, indent=2, ensure_ascii=False)
        print("📄 Resposta completa salva em 'resposta_api_completa.json'")

        if "produtos" not in resultado:
            print("❌ Estrutura inválida: não contém 'produtos'")
            return None

        produtos = resultado["produtos"]

        # Buscar ESPECIFICAMENTE por produtos de renda estimada PF
        produtos_renda_pf = [
            "renda-estimada-pf",
            "rendaEstimadaPf",
            "renda_estimada_pf",
            "rendaEstimadaPessoaFisica"
        ]

        produto_renda = None
        produto_nome = None

        for produto_pf in produtos_renda_pf:
            if produto_pf in produtos:
                produto_renda = produtos[produto_pf]
                produto_nome = produto_pf
                print(f"✅ Produto de renda PF encontrado: {produto_nome}")
                break

        if not produto_renda:
            print("❌ Nenhum produto de renda estimada PF encontrado")
            print(f"Produtos disponíveis: {list(produtos.keys())}")
            return None

        print(f"📊 Dados do produto {produto_nome}: {produto_renda}")

        # Campos ESPECÍFICOS para renda estimada PF
        campos_renda_pf = [
            "rendaEstimada",
            "renda_estimada",
            "valorRendaEstimada",
            "rendaEstimadaValor",
            "rendaMensal",
            "renda_mensal",
            "valor",
            "valorRenda"
        ]

        for campo in campos_renda_pf:
            if campo in produto_renda:
                valor_renda = produto_renda[campo]
                print(f"💰 Campo '{campo}' encontrado: {valor_renda}")

                # Processar valor
                valor_processado = processar_valor_renda(valor_renda)
                if valor_processado and valor_processado > 0:
                    print(f"✅ Renda estimada PF extraída: R$ {valor_processado:.2f}")
                    return valor_processado

        print(f"⚠️  Não foi possível extrair valor do produto {produto_nome}")
        print(f"Campos disponíveis: {list(produto_renda.keys()) if isinstance(produto_renda, dict) else 'Não é um objeto'}")
        return None

    except Exception as e:
        print(f"❌ Erro ao extrair renda estimada PF: {str(e)}")
        return None

def processar_valor_renda(valor_renda):
    """
    Processa e valida valor de renda extraído da API
    """
    try:
        # Se for um objeto, tentar extrair o valor
        if isinstance(valor_renda, dict):
            if "valor" in valor_renda:
                valor_renda = valor_renda["valor"]
            elif "amount" in valor_renda:
                valor_renda = valor_renda["amount"]
            elif "valorNumerico" in valor_renda:
                valor_renda = valor_renda["valorNumerico"]
            else:
                print(f"⚠️  Objeto sem campo de valor reconhecido: {valor_renda}")
                return None

        # Converter para float se for string
        if isinstance(valor_renda, str):
            import re
            valor_limpo = re.sub(r'[^\d,.]', '', valor_renda)
            valor_limpo = valor_limpo.replace(',', '.')
            try:
                valor_renda = float(valor_limpo)
            except ValueError:
                print(f"❌ Não foi possível converter para número: {valor_renda}")
                return None

        # Validar se é um número válido
        if isinstance(valor_renda, (int, float)) and valor_renda > 0:
            return float(valor_renda)
        else:
            print(f"⚠️  Valor inválido: {valor_renda}")
            return None

    except Exception as e:
        print(f"❌ Erro ao processar valor: {str(e)}")
        return None

def main():
    """
    Função principal de teste
    """
    print("🚀 Iniciando teste da API Serasa")
    print("=" * 50)
    
    # Verificar credenciais
    if not SERASA_CLIENT_ID or not SERASA_CLIENT_SECRET:
        print("❌ Credenciais não encontradas no arquivo .env")
        print("Configure SERASA_CLIENT_ID e SERASA_CLIENT_SECRET")
        return
    
    print(f"🔑 Client ID: {SERASA_CLIENT_ID[:10]}...")
    
    # Obter token
    access_token = obter_token_acesso()
    if not access_token:
        print("❌ Falha na autenticação. Verifique as credenciais.")
        return
    
    # CPF de teste (substitua por um CPF válido para teste)
    cpf_teste = "12345678901"  # SUBSTITUA POR UM CPF REAL PARA TESTE
    
    print(f"\n📋 Testando com CPF: {cpf_teste}")
    print("⚠️  ATENÇÃO: Substitua por um CPF real para teste válido")
    
    # Realizar consulta
    resultado = consultar_renda_api(cpf_teste, access_token)
    
    if resultado:
        print("\n📊 Resultado da consulta:")
        print(json.dumps(resultado, indent=2, ensure_ascii=False)[:500] + "...")
        
        # Extrair renda
        renda = extrair_renda_do_resultado(resultado)
        
        if renda:
            # Aplicar limite e formatação
            renda_limitada = min(renda, 10000.00)
            renda_formatada = f"{renda_limitada:,.2f}".replace(",", "X").replace(".", ",").replace("X", ".")
            
            print(f"\n💰 Resultado Final:")
            print(f"   Renda bruta: R$ {renda:.2f}")
            print(f"   Renda limitada: R$ {renda_limitada:.2f}")
            print(f"   Renda formatada: {renda_formatada}")
            
            print("\n✅ Teste concluído com sucesso!")
        else:
            print("\n❌ Falha na extração da renda")
    else:
        print("\n❌ Falha na consulta")
    
    print("\n" + "=" * 50)
    print("🏁 Teste finalizado")

if __name__ == "__main__":
    main()
