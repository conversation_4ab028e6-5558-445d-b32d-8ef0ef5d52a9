﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="JornadaRPA.Bot" sap2010:ExpressionActivityEditor.ExpressionActivityEditor="C#" sap2010:WorkflowViewState.IdRef="JornadaRPAStudio.Workflow_1"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:jdd="clr-namespace:JornadaRPA.Desktop.DesktopBot;assembly=JornadaRPA.Desktop"
 xmlns:jdg="clr-namespace:JornadaRPA.DotENV.Get_DotEnv_Value;assembly=JornadaRPA.DotENV"
 xmlns:jdl="clr-namespace:JornadaRPA.DotENV.Load_DotENV;assembly=JornadaRPA.DotENV"
 xmlns:jdo="clr-namespace:JornadaRPA.Desktop.OpenApplication;assembly=JornadaRPA.Desktop"
 xmlns:jdw="clr-namespace:JornadaRPA.Desktop.Wait;assembly=JornadaRPA.Desktop"
 xmlns:jif="clr-namespace:JornadaRPA.Image.Find;assembly=JornadaRPA.Image"
 xmlns:jks="clr-namespace:JornadaRPA.Keyboard.Send_Hotkey;assembly=JornadaRPA.Keyboard"
 xmlns:jkt="clr-namespace:JornadaRPA.Keyboard.Type_Into;assembly=JornadaRPA.Keyboard"
 xmlns:jmc="clr-namespace:JornadaRPA.Mouse.Click;assembly=JornadaRPA.Mouse"
 xmlns:jpc="clr-namespace:JornadaRPA.Python.CodePython;assembly=JornadaRPA.Python"
 xmlns:jpc1="clr-namespace:JornadaRPA.Python.Continue;assembly=JornadaRPA.Python"
 xmlns:jpe="clr-namespace:JornadaRPA.Python.Else;assembly=JornadaRPA.Python"
 xmlns:jpf="clr-namespace:JornadaRPA.Python.ForEach;assembly=JornadaRPA.Python"
 xmlns:jpi="clr-namespace:JornadaRPA.Python.If_Container;assembly=JornadaRPA.Python"
 xmlns:jpi1="clr-namespace:JornadaRPA.Python.If;assembly=JornadaRPA.Python"
 xmlns:jpt="clr-namespace:JornadaRPA.Python.Try_Container;assembly=JornadaRPA.Python"
 xmlns:jpt1="clr-namespace:JornadaRPA.Python.Try;assembly=JornadaRPA.Python"
 xmlns:jpt2="clr-namespace:JornadaRPA.Python.Try_Catch;assembly=JornadaRPA.Python"
 xmlns:jpt3="clr-namespace:JornadaRPA.Python.Try_Finally;assembly=JornadaRPA.Python"
 xmlns:jpw="clr-namespace:JornadaRPA.Python.Wait;assembly=JornadaRPA.Python"
 xmlns:jpw1="clr-namespace:JornadaRPA.Python.While;assembly=JornadaRPA.Python"
 xmlns:jwf="clr-namespace:JornadaRPA.Web.Find_Element;assembly=JornadaRPA.Web"
 xmlns:jwo="clr-namespace:JornadaRPA.Web.OpenBrowser;assembly=JornadaRPA.Web"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_67">
    <jpc:CodePython Code="# Configuração do Logger dentro do método bot&#xA;log_dir = &quot;C:\\RPA\\AtualizacaoCadastral\\logs&quot;&#xA;        &#xA;# Gerar nome do arquivo de log com data e hora para garantir que o log não sobrescreva&#xA;log_filename = datetime.now().strftime(&quot;%Y-%m-%d_%H-%M-%S&quot;)  # Formato de data e hora: YYYY-MM-DD_HH-MM-SS&#xA;log_file = os.path.join(log_dir, f&quot;bot_log_{log_filename}.log&quot;)  # Nome do arquivo com data e hora&#xA;&#xA;# Criar o diretório de logs caso não exista&#xA;if not os.path.exists(log_dir):&#xA;     os.makedirs(log_dir)&#xA;&#xA;# Configurar o logger&#xA;loggerBot = logging.getLogger(&quot;Description logger&quot;)&#xA;loggerBot.setLevel(logging.DEBUG)&#xA;&#xA;# Configurar o handler com arquivo rotativo&#xA;rotating_handler = RotatingFileHandler(log_file, maxBytes=1000000, backupCount=3)&#xA;rotating_handler.setLevel(logging.DEBUG)&#xA;&#xA;# Configurar o formato do log&#xA;formatter = logging.Formatter(&quot;%(asctime)s - %(name)s - %(levelname)s - %(message)s&quot;)&#xA;rotating_handler.setFormatter(formatter)&#xA;loggerBot.addHandler(rotating_handler)&#xA;&#xA;# Adicionar handler para exibir no console&#xA;console_handler = logging.StreamHandler()&#xA;console_handler.setLevel(logging.INFO)&#xA;console_handler.setFormatter(formatter)&#xA;loggerBot.addHandler(console_handler)" sap2010:WorkflowViewState.IdRef="CodePython_189" />
    <jpc:CodePython Code="# Tentativa de conexão com o banco de dados&#xA;try:&#xA;    conexao = mysql.connector.connect(&#xA;        host='rpa.sicoobcredilivre.com.br',&#xA;        user='rpa',&#xA;        password='sicoob@123',&#xA;        database='rpa',&#xA;    )&#xA;    cursor = conexao.cursor()&#xA;&#xA;    # Registrar sucesso no log&#xA;    loggerBot.info(&quot;Conexão com o banco de dados estabelecida com sucesso.&quot;)&#xA;&#xA;except mysql.connector.Error as err:&#xA;    # Registrar erro no log&#xA;    loggerBot.error(f&quot;Erro ao conectar com o banco de dados: {err}&quot;)" DisplayName="CONEXAO COM O BANCO DE DADOS" sap2010:WorkflowViewState.IdRef="CodePython_264" />
    <jpc:CodePython Code="def atualizar_renda():&#xA;    # Find Activity&#xA;    cpfNaBase = deskBot.find(&quot;cpfNaBase&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # If Activity&#xA;    if cpfNaBase:&#xA;        # Custom Python Code Activity&#xA;        loggerBot.error(&quot;CPF não encontrado na base da cooperativa.&quot;)&#xA;&#xA;        # Find Activity&#xA;        okCPFnaBase = deskBot.find(&quot;okCPFnaBase&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;        # Find And Click Activity&#xA;        deskBot.click()&#xA;&#xA;        # Wait Activity&#xA;        deskBot.wait(2000)&#xA;&#xA;        # Find And Click Activity&#xA;        deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Custom Python Code Activity&#xA;    loggerBot.error(&quot;Realizando alteração da renda&quot;)&#xA;&#xA;    # Custom Python Code Activity&#xA;    # Clica em Empregador&#xA;    x, y = 243, 324&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.space(wait=0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Clica em Tipo de Renda&#xA;    x, y = 250, 354&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;OUTROS&quot;, wait=0)&#xA;&#xA;    # Clica em Tipo de Renda novamente&#xA;    x, y = 250, 354&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;    deskBot.control_c(wait=0)&#xA;    deskBot.control_v(wait=0)&#xA;&#xA;    # Clica em Renda Bruta Mensal&#xA;    x, y = 232, 382&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Função melhorada para extrair renda do PDF com tratamento de encoding&#xA;    def extrair_renda_pdf(extractTextResult):&#xA;        &quot;&quot;&quot;&#xA;        Função melhorada para extrair valor da renda do texto do PDF com tratamento de encoding&#xA;        &quot;&quot;&quot;&#xA;        try:&#xA;            # Limpar caracteres especiais e normalizar o texto&#xA;            import unicodedata&#xA;            &#xA;            # Remover caracteres de controle e especiais&#xA;            texto_limpo = ''.join(char for char in extractTextResult if unicodedata.category(char)[0] != 'C')&#xA;            &#xA;            # Normalizar unicode&#xA;            texto_limpo = unicodedata.normalize('NFKD', texto_limpo)&#xA;            &#xA;            # Remover acentos e caracteres especiais&#xA;            texto_limpo = ''.join(char for char in texto_limpo if ord(char) &lt; 128 or char.isalnum() or char in ' .,R$')&#xA;            &#xA;            # Normalizar espaços&#xA;            texto_limpo = ' '.join(texto_limpo.split())&#xA;            &#xA;        except Exception as e:&#xA;            print(f&quot;Erro na limpeza do texto: {e}&quot;)&#xA;            # Fallback: usar texto original com limpeza básica&#xA;            texto_limpo = ' '.join(str(extractTextResult).split())&#xA;        &#xA;        # Múltiplos padrões de regex para diferentes formatos&#xA;        padroes_renda = [&#xA;            # Padrão original: &quot;Renda Estimada ... R$ valor&quot;&#xA;            r&quot;Renda Estimada.*?R\$\s*(\d[\d\.,]*)&quot;,&#xA;            &#xA;            # Padrão para quando está em linhas separadas: &quot;Renda Estimada&quot; seguido de &quot;R$ valor&quot;&#xA;            r&quot;Renda [Ee]stimada.*?R\$\s*(\d[\d\.,]*)&quot;,&#xA;            &#xA;            # Padrão mais flexível: qualquer &quot;R$ valor&quot; após &quot;Renda&quot;&#xA;            r&quot;Renda.*?R\$\s*(\d[\d\.,]*)&quot;,&#xA;            &#xA;            # Padrão específico para o caso atual: &quot;R$ valor&quot; seguido de &quot;Renda estimada&quot;&#xA;            r&quot;R\$\s*(\d[\d\.,]*)\s*Renda estimada&quot;,&#xA;            &#xA;            # Padrão ainda mais específico para capturar &quot;R$ 1411&quot; isolado&#xA;            r&quot;R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)&quot;,&#xA;            &#xA;            # Padrão genérico para R$ seguido de números próximo a &quot;Renda&quot;&#xA;            r&quot;(?:Renda|renda).*?R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)&quot;,&#xA;            &#xA;            # Padrão reverso: R$ valor antes de &quot;Renda&quot;&#xA;            r&quot;R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)&quot;&#xA;        ]&#xA;        &#xA;        for padrao in padroes_renda:&#xA;            try:&#xA;                match = re.search(padrao, texto_limpo, re.IGNORECASE)&#xA;                if match:&#xA;                    valor_bruto = match.group(1)&#xA;                    # Limpar pontos de milhares e vírgulas&#xA;                    valor_limpo = valor_bruto.replace('.', '').replace(',', '.')&#xA;                    try:&#xA;                        return float(valor_limpo)&#xA;                    except ValueError:&#xA;                        continue&#xA;            except Exception as e:&#xA;                print(f&quot;Erro no padrão {padrao}: {e}&quot;)&#xA;                continue&#xA;        &#xA;        return None&#xA;&#xA;    # Ler PDF e extrair o valor da Renda Serasa&#xA;    try:&#xA;        print(f&quot;Conteúdo da lista 'linha': {linha}&quot;)&#xA;    except UnicodeEncodeError:&#xA;        print(&quot;Conteúdo da lista 'linha': [dados com caracteres especiais]&quot;)&#xA;&#xA;    valorRenda = &quot;&quot;&#xA;&#xA;    if len(linha) &gt;= 3:&#xA;        pdf_path = f&quot;C:\\RPA\\AtualizacaoCadastral\\consultas\\[{str(linha[1]).strip()}].pdf&quot;&#xA;&#xA;        if os.path.exists(pdf_path):&#xA;            import pdfplumber&#xA;            import re&#xA;            import unicodedata&#xA;&#xA;            try:&#xA;                with pdfplumber.open(pdf_path) as instancePDF:&#xA;                    for page in instancePDF.pages:&#xA;                        extractTextResult = page.extract_text(&#xA;                            x_tolerance=3,&#xA;                            y_tolerance=3,&#xA;                            layout=False,&#xA;                            x_density=7.25,&#xA;                            y_density=13&#xA;                        )&#xA;&#xA;                        if not extractTextResult:&#xA;                            continue&#xA;&#xA;                        try:&#xA;                            print(f&quot;Texto extraído da página:\n{extractTextResult}\n&quot;)&#xA;                        except UnicodeEncodeError:&#xA;                            print(&quot;Texto extraído da página: [contém caracteres especiais]&quot;)&#xA;&#xA;                        # Usar a função melhorada de extração&#xA;                        renda_float = extrair_renda_pdf(extractTextResult)&#xA;                        &#xA;                        if renda_float:&#xA;                            print(f&quot;Renda extraída bruta: {renda_float}&quot;)&#xA;                            valorRenda = &quot;10.000,00&quot; if renda_float &gt; 10000 else f&quot;{renda_float:,.2f}&quot;.replace(&quot;,&quot;, &quot;X&quot;).replace(&quot;.&quot;, &quot;,&quot;).replace(&quot;X&quot;, &quot;.&quot;)&#xA;                            print(f&quot;Renda formatada: {valorRenda}&quot;)&#xA;                            loggerBot.info(f&quot;Renda extraída com sucesso: {valorRenda}&quot;)&#xA;                            break&#xA;&#xA;            except Exception as e:&#xA;                print(f&quot;Erro ao processar PDF: {e}&quot;)&#xA;                loggerBot.error(f&quot;Erro ao processar PDF {pdf_path}: {e}&quot;)&#xA;&#xA;            if not valorRenda:&#xA;                print(&quot;Não foram encontrados valores de renda conforme esperado.&quot;)&#xA;                loggerBot.warning(f&quot;Falha na extração de renda para CPF {linha[1]}. Verificar PDF manualmente.&quot;)&#xA;        else:&#xA;            print(f&quot;Erro: O arquivo PDF não foi encontrado: {pdf_path}&quot;)&#xA;            loggerBot.error(f&quot;Arquivo PDF não encontrado: {pdf_path}&quot;)&#xA;    else:&#xA;        print(f&quot;Erro: A lista 'linha' não possui 3 elementos. Tamanho atual: {len(linha)}&quot;)&#xA;        loggerBot.error(f&quot;Lista 'linha' inválida. Tamanho: {len(linha)}&quot;)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=valorRenda, wait=0)&#xA;&#xA;    # Clica em Descrição&#xA;    x, y = 323, 474&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;RENDA ESTIMADA PROVENIENTE DE BASE ESPECIALIZADA EM ENRIQUECIMENTO DE INFORMAÇÕES, CONFORME CONSULTA SERASA PRÓ EM ANEXO.&quot;, wait=0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Seleciona o tipo de renda como Variável&#xA;    x, y = 170, 569&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Clica na aba Documentos&#xA;    x, y = 179, 213&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Clica no botão Adicionar&#xA;    x, y = 1645, 286&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(4000)&#xA;&#xA;    # Clica no botão Adicionar Arquivos&#xA;    x, y = 520, 800&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(5000)&#xA;&#xA;    # Find Activity&#xA;    caminhoPDF = deskBot.find(&quot;caminhoPDF&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;C:\RPA\AtualizacaoCadastral\consultas&quot;, wait=0)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.key_enter(wait=0)&#xA;&#xA;    # Find Activity&#xA;    nomeFile = deskBot.find(&quot;nomeFile&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;[&quot;+linha[1]+&quot;].pdf&quot;, wait=0)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.key_enter(wait=0)&#xA;&#xA;    # Find Activity&#xA;    okAnexar = deskBot.find(&quot;okAnexar&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Clica no botão Enviar para enviar documentação&#xA;    x, y = 1472, 961&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(5000)&#xA;&#xA;    # Clica no botão Gravar&#xA;    x, y = 1888, 916&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(4000)&#xA;&#xA;    # Find Activity&#xA;    valorRendaIncomp = deskBot.find(&quot;valorRendaIncomp&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # If Activity&#xA;    if valorRendaIncomp:&#xA;        btOkRendaIncomp = deskBot.find(&quot;btOkRendaIncomp&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;        deskBot.click()&#xA;        deskBot.wait(1500)&#xA;&#xA;    # Clica no botão Parceiro de Negócios&#xA;    x, y = 22, 239&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Clica no botão Parceiro de Negócios novamente&#xA;    x, y = 22, 239&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Find Activity&#xA;    encAutorizacao = deskBot.find(&quot;encAutorizacao&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(4000)&#xA;&#xA;    # Clica em Encaminhar para Autorização&#xA;    x, y = 1828, 229&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Find Activity&#xA;    okEncaminhar = deskBot.find(&quot;okEncaminhar&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(6000)&#xA;&#xA;    # Clica em Fechar a Plataforma de Atendimento&#xA;    x, y = 1896, 10&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(5000)&#xA;    &#xA;    return valorRenda" DisplayName="FUNÇÃO ATUALIZAR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_294" />
    <jpc:CodePython Code="def nova_renda():&#xA;    # Find Activity&#xA;    cpfNaBase = deskBot.find(&quot;cpfNaBase&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # If Activity&#xA;    if cpfNaBase:&#xA;        # Custom Python Code Activity&#xA;        loggerBot.error(&quot;CPF não encontrado na base da cooperativa.&quot;)&#xA;&#xA;        # Find Activity&#xA;        okCPFnaBase = deskBot.find(&quot;okCPFnaBase&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;        # Find And Click Activity&#xA;        deskBot.click()&#xA;&#xA;        # Wait Activity&#xA;        deskBot.wait(2000)&#xA;&#xA;        # Find And Click Activity&#xA;        deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Custom Python Code Activity&#xA;    loggerBot.error(&quot;Realizando inclusão da renda&quot;)&#xA;&#xA;    # Custom Python Code Activity&#xA;    loggerBot.error(&quot;Selecionando Empregador&quot;)&#xA;    &#xA;    # Clica em Empregador&#xA;    x, y = 267, 269&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.space(wait=0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;    &#xA;    loggerBot.error(&quot;Selecionando Tipo de Renda&quot;)&#xA;&#xA;    # Clica em Tipo de Renda&#xA;    x, y = 296, 296&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;OUTROS&quot;, wait=0)&#xA;&#xA;    # Clica em Tipo de Renda novamente&#xA;    x, y = 296, 296&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;    deskBot.control_c(wait=0)&#xA;    deskBot.control_v(wait=0)&#xA;    &#xA;    loggerBot.error(&quot;Selecionando Renda Bruta Mensal&quot;)&#xA;&#xA;    # Clica em Renda Bruta Mensal&#xA;    x, y = 269, 325&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Função melhorada para extrair renda do PDF com tratamento de encoding&#xA;    def extrair_renda_pdf(extractTextResult):&#xA;        &quot;&quot;&quot;&#xA;        Função melhorada para extrair valor da renda do texto do PDF com tratamento de encoding&#xA;        &quot;&quot;&quot;&#xA;        try:&#xA;            # Limpar caracteres especiais e normalizar o texto&#xA;            import unicodedata&#xA;            &#xA;            # Remover caracteres de controle e especiais&#xA;            texto_limpo = ''.join(char for char in extractTextResult if unicodedata.category(char)[0] != 'C')&#xA;            &#xA;            # Normalizar unicode&#xA;            texto_limpo = unicodedata.normalize('NFKD', texto_limpo)&#xA;            &#xA;            # Remover acentos e caracteres especiais&#xA;            texto_limpo = ''.join(char for char in texto_limpo if ord(char) &lt; 128 or char.isalnum() or char in ' .,R$')&#xA;            &#xA;            # Normalizar espaços&#xA;            texto_limpo = ' '.join(texto_limpo.split())&#xA;            &#xA;        except Exception as e:&#xA;            print(f&quot;Erro na limpeza do texto: {e}&quot;)&#xA;            # Fallback: usar texto original com limpeza básica&#xA;            texto_limpo = ' '.join(str(extractTextResult).split())&#xA;        &#xA;        # Múltiplos padrões de regex para diferentes formatos&#xA;        padroes_renda = [&#xA;            # Padrão original: &quot;Renda Estimada ... R$ valor&quot;&#xA;            r&quot;Renda Estimada.*?R\$\s*(\d[\d\.,]*)&quot;,&#xA;            &#xA;            # Padrão para quando está em linhas separadas: &quot;Renda Estimada&quot; seguido de &quot;R$ valor&quot;&#xA;            r&quot;Renda [Ee]stimada.*?R\$\s*(\d[\d\.,]*)&quot;,&#xA;            &#xA;            # Padrão mais flexível: qualquer &quot;R$ valor&quot; após &quot;Renda&quot;&#xA;            r&quot;Renda.*?R\$\s*(\d[\d\.,]*)&quot;,&#xA;            &#xA;            # Padrão específico para o caso atual: &quot;R$ valor&quot; seguido de &quot;Renda estimada&quot;&#xA;            r&quot;R\$\s*(\d[\d\.,]*)\s*Renda estimada&quot;,&#xA;            &#xA;            # Padrão ainda mais específico para capturar &quot;R$ 1411&quot; isolado&#xA;            r&quot;R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)&quot;,&#xA;            &#xA;            # Padrão genérico para R$ seguido de números próximo a &quot;Renda&quot;&#xA;            r&quot;(?:Renda|renda).*?R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)&quot;,&#xA;            &#xA;            # Padrão reverso: R$ valor antes de &quot;Renda&quot;&#xA;            r&quot;R\$\s*(\d+(?:\.\d{3})*(?:,\d{2})?)\s*(?:Renda|renda)&quot;&#xA;        ]&#xA;        &#xA;        for padrao in padroes_renda:&#xA;            try:&#xA;                match = re.search(padrao, texto_limpo, re.IGNORECASE)&#xA;                if match:&#xA;                    valor_bruto = match.group(1)&#xA;                    # Limpar pontos de milhares e vírgulas&#xA;                    valor_limpo = valor_bruto.replace('.', '').replace(',', '.')&#xA;                    try:&#xA;                        return float(valor_limpo)&#xA;                    except ValueError:&#xA;                        continue&#xA;            except Exception as e:&#xA;                print(f&quot;Erro no padrão {padrao}: {e}&quot;)&#xA;                continue&#xA;        &#xA;        return None&#xA;&#xA;    # Ler PDF e extrair o valor da Renda Serasa&#xA;    try:&#xA;        print(f&quot;Conteúdo da lista 'linha': {linha}&quot;)&#xA;    except UnicodeEncodeError:&#xA;        print(&quot;Conteúdo da lista 'linha': [dados com caracteres especiais]&quot;)&#xA;&#xA;    valorRenda = &quot;&quot;&#xA;&#xA;    if len(linha) &gt;= 3:&#xA;        pdf_path = f&quot;C:\\RPA\\AtualizacaoCadastral\\consultas\\[{str(linha[1]).strip()}].pdf&quot;&#xA;&#xA;        if os.path.exists(pdf_path):&#xA;            import pdfplumber&#xA;            import re&#xA;            import unicodedata&#xA;&#xA;            try:&#xA;                with pdfplumber.open(pdf_path) as instancePDF:&#xA;                    for page in instancePDF.pages:&#xA;                        extractTextResult = page.extract_text(&#xA;                            x_tolerance=3,&#xA;                            y_tolerance=3,&#xA;                            layout=False,&#xA;                            x_density=7.25,&#xA;                            y_density=13&#xA;                        )&#xA;&#xA;                        if not extractTextResult:&#xA;                            continue&#xA;&#xA;                        try:&#xA;                            print(f&quot;Texto extraído da página:\n{extractTextResult}\n&quot;)&#xA;                        except UnicodeEncodeError:&#xA;                            print(&quot;Texto extraído da página: [contém caracteres especiais]&quot;)&#xA;&#xA;                        # Usar a função melhorada de extração&#xA;                        renda_float = extrair_renda_pdf(extractTextResult)&#xA;                        &#xA;                        if renda_float:&#xA;                            print(f&quot;Renda extraída bruta: {renda_float}&quot;)&#xA;                            valorRenda = &quot;10.000,00&quot; if renda_float &gt; 10000 else f&quot;{renda_float:,.2f}&quot;.replace(&quot;,&quot;, &quot;X&quot;).replace(&quot;.&quot;, &quot;,&quot;).replace(&quot;X&quot;, &quot;.&quot;)&#xA;                            print(f&quot;Renda formatada: {valorRenda}&quot;)&#xA;                            loggerBot.info(f&quot;Renda extraída com sucesso: {valorRenda}&quot;)&#xA;                            break&#xA;&#xA;            except Exception as e:&#xA;                print(f&quot;Erro ao processar PDF: {e}&quot;)&#xA;                loggerBot.error(f&quot;Erro ao processar PDF {pdf_path}: {e}&quot;)&#xA;&#xA;            if not valorRenda:&#xA;                print(&quot;Não foram encontrados valores de renda conforme esperado.&quot;)&#xA;                loggerBot.warning(f&quot;Falha na extração de renda para CPF {linha[1]}. Verificar PDF manualmente.&quot;)&#xA;        else:&#xA;            print(f&quot;Erro: O arquivo PDF não foi encontrado: {pdf_path}&quot;)&#xA;            loggerBot.error(f&quot;Arquivo PDF não encontrado: {pdf_path}&quot;)&#xA;    else:&#xA;        print(f&quot;Erro: A lista 'linha' não possui 3 elementos. Tamanho atual: {len(linha)}&quot;)&#xA;        loggerBot.error(f&quot;Lista 'linha' inválida. Tamanho: {len(linha)}&quot;)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=valorRenda, wait=0)&#xA;&#xA;    # Clica em Descrição&#xA;    x, y = 323, 414&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(1000)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.control_a(wait=0)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;RENDA ESTIMADA PROVENIENTE DE BASE ESPECIALIZADA EM ENRIQUECIMENTO DE INFORMAÇÕES, CONFORME CONSULTA SERASA PRÓ EM ANEXO.&quot;, wait=0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Seleciona o tipo de renda como Variável&#xA;    x, y = 170, 569&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Clica na aba Documentos&#xA;    x, y = 179, 213&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Clica no botão Adicionar&#xA;    x, y = 1645, 286&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(4000)&#xA;&#xA;    # Clica no botão Adicionar Arquivos&#xA;    x, y = 520, 800&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(5000)&#xA;&#xA;    # Find Activity&#xA;    caminhoPDF = deskBot.find(&quot;caminhoPDF&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;C:\RPA\AtualizacaoCadastral\consultas&quot;, wait=0)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.key_enter(wait=0)&#xA;&#xA;    # Find Activity&#xA;    nomeFile = deskBot.find(&quot;nomeFile&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Type Into Activity&#xA;    deskBot.paste(text=&quot;[&quot;+linha[1]+&quot;].pdf&quot;, wait=0)&#xA;&#xA;    # Send Hotkey Activity&#xA;    deskBot.key_enter(wait=0)&#xA;&#xA;    # Find Activity&#xA;    okAnexar = deskBot.find(&quot;okAnexar&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(2000)&#xA;&#xA;    # Clica no botão Enviar para enviar documentação&#xA;    x, y = 1472, 961&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(5000)&#xA;&#xA;    # Clica no botão Gravar&#xA;    x, y = 1888, 916&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(4000)&#xA;&#xA;    # Find Activity&#xA;    valorRendaIncomp = deskBot.find(&quot;valorRendaIncomp&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # If Activity&#xA;    if valorRendaIncomp:&#xA;        btOkRendaIncomp = deskBot.find(&quot;btOkRendaIncomp&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.8, waiting_time = 10000, best = True, grayscale = False)&#xA;        deskBot.click()&#xA;        deskBot.wait(1500)&#xA;&#xA;    # Clica no botão Parceiro de Negócios&#xA;    x, y = 22, 239&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Clica no botão Parceiro de Negócios novamente&#xA;    x, y = 22, 239&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Find Activity&#xA;    encAutorizacao = deskBot.find(&quot;encAutorizacao&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(4000)&#xA;&#xA;    # Clica em Encaminhar para Autorização&#xA;    x, y = 1828, 229&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(3000)&#xA;&#xA;    # Find Activity&#xA;    okEncaminhar = deskBot.find(&quot;okEncaminhar&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(6000)&#xA;&#xA;    # Clica em Fechar a Plataforma de Atendimento&#xA;    x, y = 1896, 10&#xA;    win32api.SetCursorPos((x, y))&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)&#xA;    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)&#xA;&#xA;    # Wait Activity&#xA;    deskBot.wait(5000)&#xA;    &#xA;    return valorRenda" DisplayName="FUNÇÃO NOVA RENDA" sap2010:WorkflowViewState.IdRef="CodePython_320" />
    <jpc:CodePython Code="def excluir_renda():&#xA;    btExcluirRenda = deskBot.find(&quot;btExcluirRenda&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Find Activity&#xA;    msgExclusao = deskBot.find(&quot;msgExclusao&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find Activity&#xA;    confExclusao = deskBot.find(&quot;confExclusao&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)&#xA;&#xA;    # Find And Click Activity&#xA;    deskBot.click()&#xA;&#xA;    # Find Activity&#xA;    naoExcluir = deskBot.find(&quot;naoExcluir&quot;, x = None, y = None, width = None, height = None, threshold = None, matching = 0.9, waiting_time = 10000, best = True, grayscale = False)" DisplayName="FUNÇÃO EXCLUIR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_301" />
    <jpt:Try_Container sap2010:WorkflowViewState.IdRef="Try_Container_2">
      <jpt:Try_Container.Body>
        <ActivityAction x:TypeArguments="x:String">
          <Sequence DisplayName="Try Structure" sap2010:WorkflowViewState.IdRef="Sequence_72">
            <jpt1:Try DisplayName="Try" sap2010:WorkflowViewState.IdRef="Try_2">
              <jpt1:Try.Body>
                <ActivityAction x:TypeArguments="x:String">
                  <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_69">
                    <Sequence DisplayName="Sequencia do RPA" sap2010:WorkflowViewState.IdRef="Sequence_1">
                      <jpc:CodePython Code="loggerBot.info(&quot;Iniciando execução do RPA...&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_194" />
                      <Sequence DisplayName="Configuração .ENV" sap2010:WorkflowViewState.IdRef="Sequence_77">
                        <jdl:Load_DotENV sap2010:WorkflowViewState.IdRef="Load_DotENV_1" />
                        <jdg:Get_DotEnv_Value sap2010:WorkflowViewState.IdRef="Get_DotEnv_Value_1" load="False" to="user_rpai" value="&quot;USER_RPAI&quot;" />
                        <jdg:Get_DotEnv_Value sap2010:WorkflowViewState.IdRef="Get_DotEnv_Value_2" load="False" to="senha_rpai" value="&quot;SENHA_RPAI&quot;" />
                        <jdg:Get_DotEnv_Value sap2010:WorkflowViewState.IdRef="Get_DotEnv_Value_5" load="False" to="user_serasa" value="&quot;USER_SERASA&quot;" />
                        <jdg:Get_DotEnv_Value sap2010:WorkflowViewState.IdRef="Get_DotEnv_Value_6" load="False" to="senha_serasa" value="&quot;SENHA_SERASA&quot;" />
                      </Sequence>
                      <jdo:OpenApplication active_only="{x:Null}" auto_id="{x:Null}" best_match="{x:Null}" class_name="{x:Null}" class_name_re="{x:Null}" control_id="{x:Null}" control_type="{x:Null}" ctrl_index="{x:Null}" enabled_only="{x:Null}" found_index="{x:Null}" framework_id="{x:Null}" handle="{x:Null}" parent="{x:Null}" predicate_func="{x:Null}" process="{x:Null}" selbackend="{x:Null}" title_re="{x:Null}" top_level_only="{x:Null}" visible_only="{x:Null}" sap2010:WorkflowViewState.IdRef="OpenApplication_1" backend="WIN_32" botInstance="deskBot" create_instance="True" path="&quot;C:\Sisbr 2.0\Sisbr 2.0.exe&quot;" timeout="60000" title="&quot;Sisbr 2.0&quot;" waitingTime="10000" windowFinded="popup_Window" windowState="NORMAL" />
                      <jpc:CodePython Code="loggerBot.info(&quot;Abrindo aplicação Sisbr 2.0&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_266" />
                      <jdw:Wait sap2010:WorkflowViewState.IdRef="Wait_251" InstanceBrowser="deskBot" wait_time="15000" />
                      <jpc:CodePython Code="x, y = 1048, 481&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada Usuário do Sisbr" sap2010:WorkflowViewState.IdRef="CodePython_236" />
                      <jdw:Wait sap2010:WorkflowViewState.IdRef="Wait_252" InstanceBrowser="deskBot" wait_time="1000" />
                      <jks:Send_Hotkey sap2010:WorkflowViewState.IdRef="Send_Hotkey_84" element_instance="deskBot" key="Control_A" wait="0" />
                      <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_53" click_before="False" element_instance="deskBot" empty_field="False" interval="0" text="user_rpai" type="Paste" with_spaces="True" />
                      <jpc:CodePython Code="x, y = 1048, 509&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada Senha do Sisbr" sap2010:WorkflowViewState.IdRef="CodePython_237" />
                      <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_54" click_before="False" element_instance="deskBot" empty_field="False" interval="0" text="senha_rpai" type="Paste" with_spaces="True" />
                      <jif:Find DisplayName="Botão Logar" sap2010:WorkflowViewState.IdRef="Find_127" InBrowser="deskBot" best="True" element_coord="btnLogar" grayscale="False" height="None" label="&quot;btnLogar&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                      <jmc:Click Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_113" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                      <jdw:Wait sap2010:WorkflowViewState.IdRef="Wait_253" InstanceBrowser="deskBot" wait_time="10000" />
                      <Sequence DisplayName="DEPURAÇÃO LOGIN SISBR" sap2010:WorkflowViewState.IdRef="Sequence_128">
                        <jif:Find sap2010:WorkflowViewState.IdRef="Find_170" InBrowser="deskBot" best="True" element_coord="dep_login" grayscale="False" height="None" label="&quot;dep_login&quot;" matching="0.8" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                        <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_22">
                          <jpi:If_Container.Body>
                            <ActivityAction x:TypeArguments="x:String">
                              <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_130">
                                <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_36" condition="dep_login">
                                  <jpi1:If.Body>
                                    <ActivityAction x:TypeArguments="x:String">
                                      <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_129">
                                        <jpc:CodePython Code="loggerBot.info(&quot;Login realizado com sucesso na aplicação Sisbr 2.0&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_267" />
                                      </Sequence>
                                    </ActivityAction>
                                  </jpi1:If.Body>
                                </jpi1:If>
                                <jpe:Else sap2010:WorkflowViewState.IdRef="Else_16">
                                  <jpe:Else.Body>
                                    <ActivityAction x:TypeArguments="x:String">
                                      <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_131">
                                        <jpc:CodePython Code="loggerBot.info(&quot;Falaha ao tentar realizar o login na aplicação Sisbr 2.0&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_268" />
                                      </Sequence>
                                    </ActivityAction>
                                  </jpe:Else.Body>
                                </jpe:Else>
                              </Sequence>
                            </ActivityAction>
                          </jpi:If_Container.Body>
                        </jpi:If_Container>
                      </Sequence>
                      <Sequence DisplayName="SEQUENCIA SERASA" sap2010:WorkflowViewState.IdRef="Sequence_141">
                        <jwo:OpenBrowser download_folder_path="{x:Null}" user_data_dir="{x:Null}" Headless="False" sap2010:WorkflowViewState.IdRef="OpenBrowser_1" Url="&quot;https://www.serasaexperian.com.br/meus-produtos/login&quot;" WebDriverPath="''" anonimous="False" arguments="[]" createInstance="True" inBrowser="CHROME" outBrowser="webBot" page_load_strategy="Normal" upWebDriver="False" />
                        <jpc:CodePython Code="loggerBot.info(&quot;Abrindo Google Chrome&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_269" />
                        <jpc:CodePython Code="loggerBot.info(&quot;Abrindo site do Serasa&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_270" />
                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_1" InstanceBrowser="webBot" wait_time="5000" />
                        <jwf:Find_Element DisplayName="Campo Usuário" sap2010:WorkflowViewState.IdRef="Find_Element_1" InstanceBrowser="webBot" by="ID" ensure_clickable="False" ensure_visible="False" instance_element="iduserLogon" selector="&quot;userLogon&quot;" waiting_time="1000" />
                        <jmc:Click Element="iduserLogon" sap2010:WorkflowViewState.IdRef="Click_1" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                        <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_55" click_before="False" element_instance="webBot" empty_field="False" interval="0" text="user_serasa" type="Paste" with_spaces="True" />
                        <jwf:Find_Element DisplayName="Campo Senha" sap2010:WorkflowViewState.IdRef="Find_Element_2" InstanceBrowser="webBot" by="ID" ensure_clickable="False" ensure_visible="False" instance_element="iduserPassword" selector="&quot;userPassword&quot;" waiting_time="1000" />
                        <jmc:Click Element="iduserPassword" sap2010:WorkflowViewState.IdRef="Click_2" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                        <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_56" click_before="False" element_instance="webBot" empty_field="False" interval="0" text="senha_serasa" type="Paste" with_spaces="True" />
                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_2" InstanceBrowser="webBot" wait_time="2000" />
                        <jwf:Find_Element DisplayName="Botão Acessar" sap2010:WorkflowViewState.IdRef="Find_Element_3" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="btn_Acessar" selector="&quot;/html/body/app-root/app-auth/ca-authentication/ca-login/main/section/div/section/div/form/div[3]/div[2]/seds-basic-button/seds-core-button/container-element/button/span[2]/div/span&quot;" waiting_time="1000" />
                        <jmc:Click Element="btn_Acessar" sap2010:WorkflowViewState.IdRef="Click_3" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_3" InstanceBrowser="webBot" wait_time="5000" />
                        <jwf:Find_Element DisplayName="Botão Pular Tour" sap2010:WorkflowViewState.IdRef="Find_Element_5" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="PularTour" selector="&quot;/html/body/div/div[2]/div/mat-dialog-container/div/div/div/mat-dialog-content/div/div[2]/div/a&quot;" waiting_time="1000" />
                        <jpi:If_Container DisplayName="Depuração Login Serasa" sap2010:WorkflowViewState.IdRef="If_Container_12">
                          <jpi:If_Container.Body>
                            <ActivityAction x:TypeArguments="x:String">
                              <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_52">
                                <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_12" condition="PularTour">
                                  <jpi1:If.Body>
                                    <ActivityAction x:TypeArguments="x:String">
                                      <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_51">
                                        <jpc:CodePython Code="loggerBot.info(&quot;Login realizado com sucesso no Serasa!&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_192" />
                                      </Sequence>
                                    </ActivityAction>
                                  </jpi1:If.Body>
                                </jpi1:If>
                                <jpe:Else sap2010:WorkflowViewState.IdRef="Else_8">
                                  <jpe:Else.Body>
                                    <ActivityAction x:TypeArguments="x:String">
                                      <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_53">
                                        <jpc:CodePython Code="loggerBot.error(&quot;Falha ao realizar o login no Serasa&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_193" />
                                      </Sequence>
                                    </ActivityAction>
                                  </jpe:Else.Body>
                                </jpe:Else>
                              </Sequence>
                            </ActivityAction>
                          </jpi:If_Container.Body>
                        </jpi:If_Container>
                        <jmc:Click Element="PularTour" sap2010:WorkflowViewState.IdRef="Click_4" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_5" InstanceBrowser="webBot" wait_time="3000" />
                        <jwf:Find_Element DisplayName="Aba Pessoas" sap2010:WorkflowViewState.IdRef="Find_Element_6" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="AbaPessoas" selector="&quot;/html/body/app-root/app-dashboard/div/ca-search/div/div/div/div/div[2]/label/button&quot;" waiting_time="1000" />
                        <jmc:Click Element="AbaPessoas" sap2010:WorkflowViewState.IdRef="Click_5" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                        <jpc:CodePython Code="loggerBot.info(&quot;Acessando aba Pessoas&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_271" />
                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_6" InstanceBrowser="webBot" wait_time="3000" />
                        <jwf:Find_Element DisplayName="Card Credit Bureau" sap2010:WorkflowViewState.IdRef="Find_Element_7" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="CreditBureau" selector="&quot;/html/body/app-root/app-dashboard/div/ca-card-list-view/div/main/section/div/ca-card-product[4]/div/div/div[3]/button&quot;" waiting_time="1000" />
                        <jmc:Click Element="CreditBureau" sap2010:WorkflowViewState.IdRef="Click_6" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                        <jpc:CodePython Code="loggerBot.info(&quot;Acessando Credit Bureau&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_272" />
                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_7" InstanceBrowser="webBot" wait_time="3000" />
                        <jpc:CodePython Code="loggerBot.info(&quot;Verificando se a opção Capacidade Mensal de Pagamento está marcada&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_273" />
                        <jpc:CodePython Code="# MonitorFolderEventHandler Activity&#xA;class MonitorFolderEventHandler(FileSystemEventHandler):&#xA;    def __init__(self, callback):&#xA;        self.callback = callback&#xA;&#xA;    def on_created(self, event):&#xA;        self.callback(event.src_path)&#xA;&#xA;# MonitorFolder Activity&#xA;class MonitorFolder:&#xA;    def __init__(self, folder_path, recursive=False):&#xA;        self.folder_path = folder_path&#xA;        self.recursive = recursive&#xA;&#xA;    def start_monitoring(self, callback):&#xA;        event_handler = MonitorFolderEventHandler(callback)&#xA;        observer = Observer()&#xA;        observer.schedule(event_handler, self.folder_path, self.recursive)&#xA;        observer.start()&#xA;&#xA;    def executeReturn(pathFile):&#xA;        time.sleep(2)  # Allow some time for the file operation&#xA;        &#xA;        # File Handler Activity - Get the file name and destination path&#xA;        fileName = os.path.basename(pathFile)&#xA;        newPath = os.path.join(&quot;C:\\RPA\\AtualizacaoCadastral\\consultas&quot;, fileName)&#xA;&#xA;        # Check if file already exists in the destination folder&#xA;        if os.path.exists(newPath):&#xA;            # If the file exists, remove the existing file&#xA;            os.remove(newPath)&#xA;            loggerBot.info(f&quot;Arquivo existente {fileName} removido.&quot;)&#xA;&#xA;        # Move the new file to the target folder&#xA;        shutil.move(pathFile, newPath)&#xA;&#xA;        # Custom Python Code Activity&#xA;        # Renaming the moved file&#xA;        newFileName = f&quot;[{linha[1]}].pdf&quot;  # Renaming based on 'linha[1]'&#xA;        newPathRenamed = os.path.join(&quot;C:\\RPA\\AtualizacaoCadastral\\consultas&quot;, newFileName)&#xA;&#xA;        # Check if the renamed file already exists&#xA;        if os.path.exists(newPathRenamed):&#xA;            # If the renamed file exists, remove it&#xA;            os.remove(newPathRenamed)&#xA;            loggerBot.info(f&quot;Arquivo {newFileName} já existia na pasta e foi removido.&quot;)&#xA;&#xA;        # Rename the file&#xA;        os.rename(newPath, newPathRenamed)&#xA;        loggerBot.info(f&quot;Arquivo renomeado para: {newFileName}&quot;)&#xA;&#xA;# Start monitoring the folder&#xA;instanceMonitor = MonitorFolder(&quot;C:\\RPA\\AtualizacaoCadastral&quot;)&#xA;instanceMonitor.start_monitoring(MonitorFolder.executeReturn)&#xA;&#xA;# Wait Activity - Optional if needed to allow the monitoring to run&#xA;webBot.wait(5000)" DisplayName="Mover PDF para pasta consultas e renomear" sap2010:WorkflowViewState.IdRef="CodePython_186" />
                        <jpc:CodePython Code="loggerBot.info(&quot;Iniciando processamento de CPFs...&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_286" />
                      </Sequence>
                      <jpw1:While sap2010:WorkflowViewState.IdRef="While_1" condition="True">
                        <jpw1:While.Body>
                          <ActivityAction x:TypeArguments="x:String">
                            <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_132">
                              <jpc:CodePython Code="# Primeiro, consultar a quantidade total de registros pendentes&#xA;consulta_pendentes = &quot;SELECT COUNT(*) FROM associados WHERE status = 1&quot;&#xA;cursor.execute(consulta_pendentes)&#xA;total_pendentes = cursor.fetchone()[0]&#xA;&#xA;if total_pendentes == 0:&#xA;     loggerBot.info(&quot;Não há mais CPFs pendentes para processamento&quot;)&#xA;     break  # Sai do loop quando não há mais registros&#xA;                &#xA;loggerBot.info(f&quot;Quantidade de CPFs pendentes: {total_pendentes}&quot;)&#xA;&#xA;# Agora sim, iniciar o processamento do próximo registro&#xA;cursor.execute(&quot;START TRANSACTION&quot;)" DisplayName="LÊ TABELA ASSOCIADOS NO BANCO DE DADOS" sap2010:WorkflowViewState.IdRef="CodePython_265" />
                              <jpt:Try_Container sap2010:WorkflowViewState.IdRef="Try_Container_3">
                                <jpt:Try_Container.Body>
                                  <ActivityAction x:TypeArguments="x:String">
                                    <Sequence DisplayName="Try Structure" sap2010:WorkflowViewState.IdRef="Sequence_136">
                                      <jpt1:Try DisplayName="Try" sap2010:WorkflowViewState.IdRef="Try_3">
                                        <jpt1:Try.Body>
                                          <ActivityAction x:TypeArguments="x:String">
                                            <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_133">
                                              <jpc:CodePython Code="# Seleciona o primeiro registro com status = 1&#xA;ler = &quot;SELECT * FROM associados WHERE status = 1 LIMIT 1&quot;&#xA;cursor.execute(ler)&#xA;dataList = cursor.fetchall()" sap2010:WorkflowViewState.IdRef="CodePython_287" />
                                              <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_23">
                                                <jpi:If_Container.Body>
                                                  <ActivityAction x:TypeArguments="x:String">
                                                    <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_138">
                                                      <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_37" condition="dataList">
                                                        <jpi1:If.Body>
                                                          <ActivityAction x:TypeArguments="x:String">
                                                            <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_137">
                                                              <jpc:CodePython Code="registro = dataList[0]&#xA;id_registro = registro[0]  # ID na primeira coluna&#xA;cpf = registro[1]  # CPF na segunda coluna&#xA;&#xA;# Atualiza o status para 9&#xA;atualizar = f&quot;UPDATE associados SET status = 9 WHERE id = {id_registro}&quot;&#xA;cursor.execute(atualizar)&#xA;                        &#xA;# Commit da transação&#xA;conexao.commit()&#xA;                        &#xA;loggerBot.info(f&quot;CPF {cpf} bloqueado para processamento&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_288" />
                                                              <jpf:ForEach sap2010:WorkflowViewState.IdRef="ForEach_1" item="linha" values="dataList">
                                                                <jpf:ForEach.Body>
                                                                  <ActivityAction x:TypeArguments="x:String">
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                                                                      <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_3">
                                                                        <jpc:CodePython Code="loggerBot.info(&quot;Iniciando consulta do CPF: &quot;+linha[1])" sap2010:WorkflowViewState.IdRef="CodePython_277" />
                                                                        <jwf:Find_Element DisplayName="Campo CPF" sap2010:WorkflowViewState.IdRef="Find_Element_8" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="idcpf" selector="&quot;/html/body/app-root/app-layout/div/app-content/app-home/div/div[1]/app-form-generate-report/div/div[1]/form/div[1]/app-form-field-document/form/mat-form-field/div[1]/div/div[3]/mat-chip-grid/div/input&quot;" waiting_time="1000" />
                                                                        <jmc:Click Element="idcpf" sap2010:WorkflowViewState.IdRef="Click_7" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_12" InstanceBrowser="webBot" wait_time="5000" />
                                                                        <jks:Send_Hotkey sap2010:WorkflowViewState.IdRef="Send_Hotkey_1" element_instance="webBot" key="Control_A" wait="1" />
                                                                        <jkt:Type_Into DisplayName="Preencher o campo CPF" sap2010:WorkflowViewState.IdRef="Type_Into_60" click_before="False" element_instance="idcpf" empty_field="False" interval="0" text="linha[1]" type="Send_Keys" with_spaces="True" />
                                                                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_4" InstanceBrowser="webBot" wait_time="3000" />
                                                                        <jwf:Find_Element DisplayName="Botão Gerar Relatório" sap2010:WorkflowViewState.IdRef="Find_Element_9" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="idbotaoConsultar" selector="&quot;/html/body/app-root/app-layout/div/app-content/app-home/div/div[1]/app-form-generate-report/div/div[2]/seds-basic-button/seds-core-button/container-element/button&quot;" waiting_time="1000" />
                                                                        <jmc:Click Element="idbotaoConsultar" sap2010:WorkflowViewState.IdRef="Click_8" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_9" InstanceBrowser="webBot" wait_time="3000" />
                                                                        <jif:Find sap2010:WorkflowViewState.IdRef="Find_1" InBrowser="webBot" best="True" element_coord="partEmpresa" grayscale="False" height="None" label="&quot;part_empresa&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                        <jpi:If_Container DisplayName="Se aparecer janela de Participação em Empresa" sap2010:WorkflowViewState.IdRef="If_Container_1">
                                                                          <jpi:If_Container.Body>
                                                                            <ActivityAction x:TypeArguments="x:String">
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_9">
                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_1" condition="partEmpresa">
                                                                                    <jpi1:If.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_8">
                                                                                          <jwf:Find_Element DisplayName="Seleciona opcao Não " sap2010:WorkflowViewState.IdRef="Find_Element_12" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="nao_partEmpresa" selector="&quot;/html/body/div/div[2]/div/form[2]/div/div[3]/div[3]/table/tbody/tr/td/table/tbody/tr/td[2]/input&quot;" waiting_time="1000" />
                                                                                          <jmc:Click Element="nao_partEmpresa" sap2010:WorkflowViewState.IdRef="Click_11" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                          <jwf:Find_Element DisplayName="Clica em Consultar" sap2010:WorkflowViewState.IdRef="Find_Element_13" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="btn_Consulta2" selector="&quot;/html/body/div/div[2]/div/form[2]/div/div[3]/div[3]/table/tbody/tr[2]/td/a/span&quot;" waiting_time="1000" />
                                                                                          <jmc:Click Element="btn_Consulta2" sap2010:WorkflowViewState.IdRef="Click_12" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                        </Sequence>
                                                                                      </ActivityAction>
                                                                                    </jpi1:If.Body>
                                                                                  </jpi1:If>
                                                                                </Sequence>
                                                                              </Sequence>
                                                                            </ActivityAction>
                                                                          </jpi:If_Container.Body>
                                                                        </jpi:If_Container>
                                                                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_13" InstanceBrowser="webBot" wait_time="3000" />
                                                                        <jwf:Find_Element DisplayName="Botão Imprimir" sap2010:WorkflowViewState.IdRef="Find_Element_10" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="btn_Imprimir" selector="&quot;/html/body/app-root/app-layout/div/app-content/app-report/app-report-core/app-report-resume/div/seds-outlined-button/seds-core-button/container-element/button&quot;" waiting_time="1000" />
                                                                        <jpi:If_Container DisplayName="Depuração Consulta Serasa" sap2010:WorkflowViewState.IdRef="If_Container_13">
                                                                          <jpi:If_Container.Body>
                                                                            <ActivityAction x:TypeArguments="x:String">
                                                                              <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_55">
                                                                                <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_13" condition="btn_Imprimir">
                                                                                  <jpi1:If.Body>
                                                                                    <ActivityAction x:TypeArguments="x:String">
                                                                                      <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_54">
                                                                                        <jpc:CodePython Code="loggerBot.info(&quot;Consulta Serasa realizada com sucesso para o CPF &quot;+linha[1])" sap2010:WorkflowViewState.IdRef="CodePython_195" />
                                                                                      </Sequence>
                                                                                    </ActivityAction>
                                                                                  </jpi1:If.Body>
                                                                                </jpi1:If>
                                                                                <jpe:Else sap2010:WorkflowViewState.IdRef="Else_9">
                                                                                  <jpe:Else.Body>
                                                                                    <ActivityAction x:TypeArguments="x:String">
                                                                                      <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_56">
                                                                                        <jpc:CodePython Code="loggerBot.error(&quot;Erro ao tentar realizar consulta.&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_196" />
                                                                                      </Sequence>
                                                                                    </ActivityAction>
                                                                                  </jpe:Else.Body>
                                                                                </jpe:Else>
                                                                              </Sequence>
                                                                            </ActivityAction>
                                                                          </jpi:If_Container.Body>
                                                                        </jpi:If_Container>
                                                                        <jmc:Click Element="btn_Imprimir" sap2010:WorkflowViewState.IdRef="Click_9" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_321" InstanceBrowser="webBot" wait_time="3000" />
                                                                        <Sequence DisplayName="SEQUENCIA SISBR" sap2010:WorkflowViewState.IdRef="Sequence_37">
                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Aguardando 5 segundos para chamar a aplicação Sisbr para frente...&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_213" />
                                                                          <jdd:DesktopBot sap2010:WorkflowViewState.IdRef="DesktopBot_3" instance="deskBot" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_230" InstanceBrowser="deskBot" wait_time="5000" />
                                                                          <jpc:CodePython Code="x, y = 1807, 64&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Trazer SISBR para Frente" sap2010:WorkflowViewState.IdRef="CodePython_81" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_102" InstanceBrowser="deskBot" wait_time="3000" />
                                                                          <jpc:CodePython Code="# Simula o clique diretamente nas coordenadas (229, 993)&#xA;x, y = 229, 993&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada Pesquisar do Sisbr" sap2010:WorkflowViewState.IdRef="CodePython_82" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_103" InstanceBrowser="deskBot" wait_time="3000" />
                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Acessando o módulo Plataforma de Atendimento&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_278" />
                                                                          <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_58" click_before="False" element_instance="deskBot" empty_field="False" interval="0" text="&quot;PLATAFORMA DE ATENDIMENTO&quot;" type="Paste" with_spaces="True" />
                                                                          <jdw:Wait sap2010:WorkflowViewState.IdRef="Wait_254" InstanceBrowser="deskBot" wait_time="2000" />
                                                                          <jif:Find DisplayName="Click Menu Plat Atendimento" sap2010:WorkflowViewState.IdRef="Find_62" InBrowser="deskBot" best="True" element_coord="clickPlatAtend" grayscale="False" height="None" label="&quot;clickPlatAtend&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                          <jmc:Click DisplayName="Click Menu Plat Atendimento" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_54" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_105" InstanceBrowser="deskBot" wait_time="7000" />
                                                                          <jif:Find DisplayName="CampoCPF" sap2010:WorkflowViewState.IdRef="Find_63" InBrowser="deskBot" best="True" element_coord="campoCPF" grayscale="False" height="None" label="&quot;campoCPF&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                          <jpi:If_Container DisplayName="Depuração Módulo Plataforma de Atendimento" sap2010:WorkflowViewState.IdRef="If_Container_14">
                                                                            <jpi:If_Container.Body>
                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_58">
                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_14" condition="campoCPF">
                                                                                    <jpi1:If.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_57">
                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Módulo Plataforma de Atendimento acessado com sucesso!&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_197" />
                                                                                        </Sequence>
                                                                                      </ActivityAction>
                                                                                    </jpi1:If.Body>
                                                                                  </jpi1:If>
                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_10">
                                                                                    <jpe:Else.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_59">
                                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Erro ao acessar o módulo Plataforma de Atendimento.&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_198" />
                                                                                        </Sequence>
                                                                                      </ActivityAction>
                                                                                    </jpe:Else.Body>
                                                                                  </jpe:Else>
                                                                                </Sequence>
                                                                              </ActivityAction>
                                                                            </jpi:If_Container.Body>
                                                                          </jpi:If_Container>
                                                                          <jmc:Click Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_55" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_250" InstanceBrowser="deskBot" wait_time="1500" />
                                                                          <jmc:Click Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_112" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                          <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_61" click_before="False" element_instance="deskBot" empty_field="False" interval="0" text="linha[1]" type="Paste" with_spaces="True" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_106" InstanceBrowser="deskBot" wait_time="2000" />
                                                                          <jif:Find DisplayName="Botão Pesquisar" sap2010:WorkflowViewState.IdRef="Find_64" InBrowser="deskBot" best="True" element_coord="btPesquisar" grayscale="False" height="None" label="&quot;btPesq&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                          <jmc:Click DisplayName="Click no botão Pesquisar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_56" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                          <jmc:Click DisplayName="Click no botão Pesquisar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_57" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Consultando o CPF &quot;+linha[1]+&quot; na Plataforma de Atendimento&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_279" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_248" InstanceBrowser="deskBot" wait_time="7000" />
                                                                          <jpc:CodePython Code="x, y = 1458, 112&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada Área Limpa" sap2010:WorkflowViewState.IdRef="CodePython_257" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_305" InstanceBrowser="deskBot" wait_time="1000" />
                                                                          <jpc:CodePython Code="# Simula o clique diretamente nas coordenadas (1330, 309)&#xA;x, y = 1330, 309&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada fechar Validacao Cadastral" sap2010:WorkflowViewState.IdRef="CodePython_255" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_303" InstanceBrowser="deskBot" wait_time="1000" />
                                                                          <jpc:CodePython Code="# Simula o clique diretamente nas coordenadas (1330, 309)&#xA;x, y = 1330, 309&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada fechar Validacao Cadastral" sap2010:WorkflowViewState.IdRef="CodePython_258" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_304" InstanceBrowser="deskBot" wait_time="2000" />
                                                                          <jpc:CodePython Code="# Coordenada Fechar Plataforma de Atendimento&#xA;x, y = 1896, 10&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Clica em Fechar a Plataforma de Atendimento" sap2010:WorkflowViewState.IdRef="CodePython_260" />
                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Fechando Plataforma de Atendimento para eliminar propagandas&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_280" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_276" InstanceBrowser="deskBot" wait_time="3000" />
                                                                          <Sequence DisplayName="ABRE PLATAFORMA ATENDIMENTO NOVAMENTE" sap2010:WorkflowViewState.IdRef="Sequence_103">
                                                                            <jpc:CodePython Code="# Simula o clique diretamente nas coordenadas (229, 993)&#xA;x, y = 229, 993&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada Pesquisar do Sisbr" sap2010:WorkflowViewState.IdRef="CodePython_248" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_277" InstanceBrowser="deskBot" wait_time="3000" />
                                                                            <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_89" click_before="False" element_instance="deskBot" empty_field="False" interval="0" text="&quot;PLATAFORMA DE ATENDIMENTO&quot;" type="Paste" with_spaces="True" />
                                                                            <jdw:Wait sap2010:WorkflowViewState.IdRef="Wait_278" InstanceBrowser="deskBot" wait_time="2000" />
                                                                            <jif:Find DisplayName="Click Menu Plat Atendimento" sap2010:WorkflowViewState.IdRef="Find_146" InBrowser="deskBot" best="True" element_coord="clickPlatAtend" grayscale="False" height="None" label="&quot;clickPlatAtend&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                            <jmc:Click DisplayName="Click Menu Plat Atendimento" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_122" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                            <jpc:CodePython Code="loggerBot.error(&quot;Abrindo o módulo Plataforma de Atendimento novamente&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_281" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_279" InstanceBrowser="deskBot" wait_time="7000" />
                                                                            <jif:Find DisplayName="CampoCPF" sap2010:WorkflowViewState.IdRef="Find_147" InBrowser="deskBot" best="True" element_coord="campoCPF" grayscale="False" height="None" label="&quot;campoCPF&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                            <jpi:If_Container DisplayName="Depuração Módulo Plataforma de Atendimento" sap2010:WorkflowViewState.IdRef="If_Container_19">
                                                                              <jpi:If_Container.Body>
                                                                                <ActivityAction x:TypeArguments="x:String">
                                                                                  <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_102">
                                                                                    <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_24" condition="campoCPF">
                                                                                      <jpi1:If.Body>
                                                                                        <ActivityAction x:TypeArguments="x:String">
                                                                                          <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_100">
                                                                                            <jpc:CodePython Code="loggerBot.info(&quot;Módulo Plataforma de Atendimento acessado com sucesso!&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_249" />
                                                                                          </Sequence>
                                                                                        </ActivityAction>
                                                                                      </jpi1:If.Body>
                                                                                    </jpi1:If>
                                                                                    <jpe:Else sap2010:WorkflowViewState.IdRef="Else_14">
                                                                                      <jpe:Else.Body>
                                                                                        <ActivityAction x:TypeArguments="x:String">
                                                                                          <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_101">
                                                                                            <jpc:CodePython Code="loggerBot.error(&quot;Erro ao acessar o módulo Plataforma de Atendimento.&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_250" />
                                                                                          </Sequence>
                                                                                        </ActivityAction>
                                                                                      </jpe:Else.Body>
                                                                                    </jpe:Else>
                                                                                  </Sequence>
                                                                                </ActivityAction>
                                                                              </jpi:If_Container.Body>
                                                                            </jpi:If_Container>
                                                                            <jmc:Click Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_123" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_280" InstanceBrowser="deskBot" wait_time="1500" />
                                                                            <jmc:Click Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_124" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                            <jkt:Type_Into sap2010:WorkflowViewState.IdRef="Type_Into_90" click_before="False" element_instance="deskBot" empty_field="False" interval="0" text="linha[1]" type="Paste" with_spaces="True" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_281" InstanceBrowser="deskBot" wait_time="2000" />
                                                                            <jif:Find DisplayName="Botão Pesquisar" sap2010:WorkflowViewState.IdRef="Find_148" InBrowser="deskBot" best="True" element_coord="btPesquisar" grayscale="False" height="None" label="&quot;btPesq&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                            <jmc:Click DisplayName="Click no botão Pesquisar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_125" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                            <jmc:Click DisplayName="Click no botão Pesquisar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_126" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_282" InstanceBrowser="deskBot" wait_time="7000" />
                                                                            <jpc:CodePython Code="loggerBot.error(&quot;Consultando o CPF &quot;+linha[1]+&quot; na Plataforma de Atendimento&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_282" />
                                                                          </Sequence>
                                                                          <Sequence DisplayName="FECHA AVALIACAO CADASTRAL NOVAMENTE" sap2010:WorkflowViewState.IdRef="Sequence_127">
                                                                            <jpc:CodePython Code="x, y = 1458, 112&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada Área Limpa" sap2010:WorkflowViewState.IdRef="CodePython_261" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_309" InstanceBrowser="deskBot" wait_time="1000" />
                                                                            <jpc:CodePython Code="# Simula o clique diretamente nas coordenadas (1330, 309)&#xA;x, y = 1330, 309&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada fechar Validacao Cadastral" sap2010:WorkflowViewState.IdRef="CodePython_262" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_310" InstanceBrowser="deskBot" wait_time="1000" />
                                                                            <jpc:CodePython Code="# Simula o clique diretamente nas coordenadas (1330, 309)&#xA;x, y = 1330, 309&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Click na coordenada fechar Validacao Cadastral" sap2010:WorkflowViewState.IdRef="CodePython_263" />
                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_312" InstanceBrowser="deskBot" wait_time="3000" />
                                                                          </Sequence>
                                                                          <jpc:CodePython Code="# Coordenada opção AVALIAÇÃO FINANCEIRA&#xA;x, y = 27, 277&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Botao AVALIACAO FINANCEIRA" sap2010:WorkflowViewState.IdRef="CodePython_86" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_111" InstanceBrowser="deskBot" wait_time="2000" />
                                                                          <jif:Find DisplayName="btRenda" sap2010:WorkflowViewState.IdRef="Find_65" InBrowser="deskBot" best="True" element_coord="btRenda" grayscale="False" height="None" label="&quot;btRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                          <jpi:If_Container DisplayName="Depuração consulta CPF - Plataforma de Atendimento" sap2010:WorkflowViewState.IdRef="If_Container_15">
                                                                            <jpi:If_Container.Body>
                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_62">
                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_15" condition="btRenda">
                                                                                    <jpi1:If.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_60">
                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;CPF &quot;+linha[1]+&quot; consultado na Plataforma de Atendimento&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_199" />
                                                                                        </Sequence>
                                                                                      </ActivityAction>
                                                                                    </jpi1:If.Body>
                                                                                  </jpi1:If>
                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_11">
                                                                                    <jpe:Else.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_61">
                                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Erro ao consultar CPF na Plataforma de Atendimento.&quot;)" sap2010:WorkflowViewState.IdRef="CodePython_200" />
                                                                                        </Sequence>
                                                                                      </ActivityAction>
                                                                                    </jpe:Else.Body>
                                                                                  </jpe:Else>
                                                                                </Sequence>
                                                                              </ActivityAction>
                                                                            </jpi:If_Container.Body>
                                                                          </jpi:If_Container>
                                                                          <jmc:Click Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_58" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_112" InstanceBrowser="deskBot" wait_time="5000" />
                                                                          <jpc:CodePython Code="# Coordenada Linha 1&#xA;x, y = 1675, 269&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 1" sap2010:WorkflowViewState.IdRef="CodePython_87" />
                                                                          <jif:Find DisplayName="Renda Ponderada" sap2010:WorkflowViewState.IdRef="Find_125" InBrowser="deskBot" best="True" element_coord="rendaPonderada" grayscale="False" height="None" label="&quot;rendaPonderada3&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                          <jpi1:If DisplayName="RENDA PONDERADA" sap2010:WorkflowViewState.IdRef="If_17" condition="rendaPonderada">
                                                                            <jpi1:If.Body>
                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_74">
                                                                                  <jpc:CodePython Code="# Coordenada Fechar Plataforma de Atendimento&#xA;x, y = 1896, 10&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse" DisplayName="Clica em Fechar a Plataforma de Atendimento" sap2010:WorkflowViewState.IdRef="CodePython_232" />
                                                                                  <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_245" InstanceBrowser="webBot" wait_time="2000" />
                                                                                  <jpc:CodePython Code="# Função para trazer a aplicação para frente&#xA;def bring_application_to_front():&#xA;    try:&#xA;        # Acessando a aplicação via AppActivate&#xA;        shell = win32com.client.Dispatch(&quot;WScript.Shell&quot;)&#xA;        shell.AppActivate(&quot;Google Chrome&quot;)  # Nome da janela da aplicação&#xA;        &#xA;        # Espera um pouco para garantir que a janela está ativa&#xA;        time.sleep(1)&#xA;&#xA;        # Obtém o identificador da janela pela classe ou título&#xA;        hwnd = win32gui.FindWindow(None, &quot;Google Chrome&quot;)  # Substitua &quot;Google Chrome&quot; com o título correto da janela&#xA;        &#xA;        if hwnd:&#xA;            # Garante que a janela seja trazida para frente&#xA;            win32gui.ShowWindow(hwnd, 5)  # 5 é SW_SHOW (mostrar janela)&#xA;            win32gui.SetForegroundWindow(hwnd)  # Traz a janela para frente&#xA;            print(&quot;Aplicação trazida para frente com sucesso!&quot;)&#xA;        else:&#xA;            print(&quot;Aplicação não encontrada!&quot;)&#xA;        &#xA;        # Tentativa adicional para garantir o foco&#xA;        time.sleep(1)  # Atraso antes da tentativa adicional&#xA;        if hwnd:&#xA;            win32gui.SetForegroundWindow(hwnd)  # Nova tentativa de trazer para frente&#xA;            print(&quot;Segunda tentativa de trazer a aplicação para frente.&quot;)&#xA;&#xA;    except Exception as e:&#xA;        print(f&quot;Erro ao trazer a aplicação para frente: {e}&quot;)&#xA;&#xA;# Chamada da função no ponto necessário do script&#xA;bring_application_to_front()" DisplayName="Trazer CHROME para Frente" sap2010:WorkflowViewState.IdRef="CodePython_231" />
                                                                                  <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_247" InstanceBrowser="webBot" wait_time="2000" />
                                                                                  <jwf:Find_Element DisplayName="Botão Nova Consulta" sap2010:WorkflowViewState.IdRef="Find_Element_19" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="btn_novaConsulta" selector="&quot;/html/body/app-root/app-layout/app-menu/div/ul/li[1]/a/span[1]/seds-icon/mat-icon&quot;" waiting_time="1000" />
                                                                                  <jmc:Click Element="btn_novaConsulta" sap2010:WorkflowViewState.IdRef="Click_111" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                  <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_246" InstanceBrowser="deskBot" wait_time="4000" />
                                                                                  <Sequence DisplayName="REGISTRO CPF NÃO ATUALIZADO - RENDA PONDERADA" sap2010:WorkflowViewState.IdRef="Sequence_75">
                                                                                    <jpc:CodePython Code="# ALTERAR STATUS PARA RENDA PONDERADA (7) NA TABELA ASSOCIADOS&#xA;&#xA;data_atualizacao = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Data e hora atual&#xA;cpf_ponderada = linha[1]&#xA;&#xA;update_ponderada = f&quot;UPDATE associados SET status = '7', data_atualizacao = '{data_atualizacao}' WHERE cpf = '{cpf_ponderada}'&quot;&#xA;cursor.execute(update_ponderada)&#xA;conexao.commit()" DisplayName="ATUALIZANDO STATUS PARA RENDA PONDERADA" sap2010:WorkflowViewState.IdRef="CodePython_230" />
                                                                                  </Sequence>
                                                                                  <jpc:CodePython Code="loggerBot.info(&quot;CPF &quot;+linha[1]+&quot; não atualizado: Renda Ponderada!&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_229" />
                                                                                  <jpc1:Continue sap2010:WorkflowViewState.IdRef="Continue_2">
                                                                                    <jpc1:Continue.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String" />
                                                                                    </jpc1:Continue.Body>
                                                                                  </jpc1:Continue>
                                                                                </Sequence>
                                                                              </ActivityAction>
                                                                            </jpi1:If.Body>
                                                                          </jpi1:If>
                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_113" InstanceBrowser="deskBot" wait_time="2000" />
                                                                          <jif:Find DisplayName="Img botão Excluir Renda" sap2010:WorkflowViewState.IdRef="Find_114" InBrowser="deskBot" best="True" element_coord="btExcluirRenda" grayscale="False" height="None" label="&quot;btExcluirRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                          <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_10">
                                                                            <jpi:If_Container.Body>
                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_47">
                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_10" condition="btExcluirRenda">
                                                                                    <jpi1:If.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                        <Sequence DisplayName="FLUXO EXCLUIR/ALTERAR RENDAS - ATÉ 4" sap2010:WorkflowViewState.IdRef="Sequence_46">
                                                                                          <jpc:CodePython Code="excluir_renda()" DisplayName="CHAMA FUNÇÃO EXCLUIR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_302" />
                                                                                          <jif:Find DisplayName="ERRO INTERNO" sap2010:WorkflowViewState.IdRef="Find_175" InBrowser="deskBot" best="True" element_coord="erroInterno" grayscale="False" height="None" label="&quot;erroInterno&quot;" matching="0.8" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                          <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_9">
                                                                                            <jpi:If_Container.Body>
                                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_36">
                                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_6" condition="erroInterno">
                                                                                                    <jpi1:If.Body>
                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                        <Sequence DisplayName="IF RENDA 1" sap2010:WorkflowViewState.IdRef="Sequence_25">
                                                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Impossível realizar exclusão. Única renda cadastrada.&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_201" />
                                                                                                          <jpc:CodePython Code="# Coordenada Clique em OK - Mensagem não é possível excluir&#xA;x, y = 1082, 596&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica em OK ***VERIFICAR COORDENADAS***" sap2010:WorkflowViewState.IdRef="CodePython_88" />
                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_114" InstanceBrowser="deskBot" wait_time="3000" />
                                                                                                          <jpc:CodePython Code="# Coordenada Linha 1&#xA;x, y = 1675, 269&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 1" sap2010:WorkflowViewState.IdRef="CodePython_89" />
                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_115" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                          <jif:Find DisplayName="Botão Alterar Renda" sap2010:WorkflowViewState.IdRef="Find_173" InBrowser="deskBot" best="True" element_coord="bt_alterarRenda" grayscale="False" height="None" label="&quot;bt_alterarRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                          <jmc:Click DisplayName="Click no botão Alterar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_144" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                                          <jpc:CodePython Code="valorRenda = atualizar_renda()" DisplayName="CHAMA A FUNÇÃO ALTERAR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_295" />
                                                                                                        </Sequence>
                                                                                                      </ActivityAction>
                                                                                                    </jpi1:If.Body>
                                                                                                  </jpi1:If>
                                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_6">
                                                                                                    <jpe:Else.Body>
                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_35">
                                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Renda excluída com sucesso!&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_202" />
                                                                                                          <jpc:CodePython Code="# Coordenada Linha 2&#xA;x, y = 1667, 292&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 2" sap2010:WorkflowViewState.IdRef="CodePython_106" />
                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_136" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                          <jpc:CodePython Code="excluir_renda()" DisplayName="CHAMA FUNÇÃO EXCLUIR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_303" />
                                                                                                          <jif:Find sap2010:WorkflowViewState.IdRef="Find_176" InBrowser="deskBot" best="True" element_coord="erroInterno" grayscale="False" height="None" label="&quot;erroInterno&quot;" matching="0.8" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                          <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_8">
                                                                                                            <jpi:If_Container.Body>
                                                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_34">
                                                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_7" condition="erroInterno">
                                                                                                                    <jpi1:If.Body>
                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                        <Sequence DisplayName="IF RENDA 2" sap2010:WorkflowViewState.IdRef="Sequence_26">
                                                                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Impossível realizar exclusão. Única renda cadastrada.&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_203" />
                                                                                                                          <jpc:CodePython Code="# Coordenada Clique em OK - Mensagem não é possível excluir&#xA;x, y = 1082, 596&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica em OK ***VERIFICAR COORDENADAS***" sap2010:WorkflowViewState.IdRef="CodePython_107" />
                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_137" InstanceBrowser="deskBot" wait_time="3000" />
                                                                                                                          <jpc:CodePython Code="# Coordenada Linha 2&#xA;x, y = 1667, 292&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 2" sap2010:WorkflowViewState.IdRef="CodePython_108" />
                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_314" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                          <jif:Find DisplayName="Botão Alterar Renda" sap2010:WorkflowViewState.IdRef="Find_172" InBrowser="deskBot" best="True" element_coord="bt_alterarRenda" grayscale="False" height="None" label="&quot;bt_alterarRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                          <jmc:Click DisplayName="Click no botão Alterar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_143" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                                                          <jpc:CodePython Code="valorRenda = atualizar_renda()" DisplayName="CHAMA FUNÇÃO ATUALIZAR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_296" />
                                                                                                                        </Sequence>
                                                                                                                      </ActivityAction>
                                                                                                                    </jpi1:If.Body>
                                                                                                                  </jpi1:If>
                                                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_5">
                                                                                                                    <jpe:Else.Body>
                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_33">
                                                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Renda excluída com sucesso!&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_204" />
                                                                                                                          <jpc:CodePython Code="# Coordenada Linha 3&#xA;x, y = 1678, 315&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 3" sap2010:WorkflowViewState.IdRef="CodePython_125" />
                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_158" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                          <jpc:CodePython Code="excluir_renda()" DisplayName="CHAMA FUNÇÃO EXCLUIR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_304" />
                                                                                                                          <jif:Find sap2010:WorkflowViewState.IdRef="Find_177" InBrowser="deskBot" best="True" element_coord="erroInterno" grayscale="False" height="None" label="&quot;erroInterno&quot;" matching="0.8" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                          <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_7">
                                                                                                                            <jpi:If_Container.Body>
                                                                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_32">
                                                                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_8" condition="erroInterno">
                                                                                                                                    <jpi1:If.Body>
                                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                                        <Sequence DisplayName="IF RENDA 3" sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                                                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Impossível realizar exclusão. Única renda cadastrada.&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_205" />
                                                                                                                                          <jpc:CodePython Code="# Coordenada Clique em OK - Mensagem não é possível excluir&#xA;x, y = 1082, 596&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica em OK ***VERIFICAR COORDENADAS***" sap2010:WorkflowViewState.IdRef="CodePython_126" />
                                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_159" InstanceBrowser="deskBot" wait_time="3000" />
                                                                                                                                          <jpc:CodePython Code="# Coordenada Linha 3&#xA;x, y = 1678, 315&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 3" sap2010:WorkflowViewState.IdRef="CodePython_127" />
                                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_160" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                                          <jif:Find DisplayName="Botão Alterar Renda" sap2010:WorkflowViewState.IdRef="Find_171" InBrowser="deskBot" best="True" element_coord="bt_alterarRenda" grayscale="False" height="None" label="&quot;bt_alterarRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                                          <jmc:Click DisplayName="Click no botão Alterar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_142" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                                                                          <jpc:CodePython Code="valorRenda = atualizar_renda()" DisplayName="CHAMA FUNÇÃO ATUALIZAR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_297" />
                                                                                                                                        </Sequence>
                                                                                                                                      </ActivityAction>
                                                                                                                                    </jpi1:If.Body>
                                                                                                                                  </jpi1:If>
                                                                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_4">
                                                                                                                                    <jpe:Else.Body>
                                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_31">
                                                                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Renda excluída com sucesso!&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_206" />
                                                                                                                                          <jpc:CodePython Code="# Coordenada Linha 4&#xA;x, y = 1678, 338&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 4" sap2010:WorkflowViewState.IdRef="CodePython_144" />
                                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_180" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                                          <jpc:CodePython Code="excluir_renda()" DisplayName="CHAMA FUNÇÃO EXCLUIR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_298" />
                                                                                                                                          <jif:Find sap2010:WorkflowViewState.IdRef="Find_178" InBrowser="deskBot" best="True" element_coord="erroInterno" grayscale="False" height="None" label="&quot;erroInterno&quot;" matching="0.8" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                                          <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_6">
                                                                                                                                            <jpi:If_Container.Body>
                                                                                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_30">
                                                                                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_9" condition="erroInterno">
                                                                                                                                                    <jpi1:If.Body>
                                                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_29">
                                                                                                                                                          <Sequence DisplayName="IF RENDA 4" sap2010:WorkflowViewState.IdRef="Sequence_28">
                                                                                                                                                            <jpc:CodePython Code="loggerBot.error(&quot;Impossível realizar exclusão. Única renda cadastrada.&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_207" />
                                                                                                                                                            <jpc:CodePython Code="# Coordenada Clique em OK - Mensagem não é possível excluir&#xA;x, y = 1082, 596&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica em OK ***VERIFICAR COORDENADAS***" sap2010:WorkflowViewState.IdRef="CodePython_145" />
                                                                                                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_181" InstanceBrowser="deskBot" wait_time="3000" />
                                                                                                                                                            <jpc:CodePython Code="# Coordenada Linha 4&#xA;x, y = 1678, 338&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 4" sap2010:WorkflowViewState.IdRef="CodePython_146" />
                                                                                                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_182" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                                                            <jif:Find DisplayName="Botão Alterar Renda" sap2010:WorkflowViewState.IdRef="Find_107" InBrowser="deskBot" best="True" element_coord="bt_alterarRenda" grayscale="False" height="None" label="&quot;bt_alterarRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                                                            <jmc:Click DisplayName="Click no botão Alterar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_88" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                                                                                            <jpc:CodePython Code="valorRenda = atualizar_renda()" DisplayName="CHAMA FUNÇÃO ATUALIZAR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_299" />
                                                                                                                                                          </Sequence>
                                                                                                                                                        </Sequence>
                                                                                                                                                      </ActivityAction>
                                                                                                                                                    </jpi1:If.Body>
                                                                                                                                                  </jpi1:If>
                                                                                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_18">
                                                                                                                                                    <jpe:Else.Body>
                                                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_142">
                                                                                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Renda excluída com sucesso!&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_305" />
                                                                                                                                                          <jpc:CodePython Code="# Coordenada Linha 5&#xA;x, y = 1678, 355&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 5 ** PEGAR COORDENADA **" sap2010:WorkflowViewState.IdRef="CodePython_306" />
                                                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_315" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                                                          <jpc:CodePython Code="excluir_renda()" DisplayName="CHAMA FUNÇÃO EXCLUIR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_307" />
                                                                                                                                                          <jif:Find sap2010:WorkflowViewState.IdRef="Find_179" InBrowser="deskBot" best="True" element_coord="erroInterno" grayscale="False" height="None" label="&quot;erroInterno&quot;" matching="0.8" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                                                          <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_24">
                                                                                                                                                            <jpi:If_Container.Body>
                                                                                                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_143">
                                                                                                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_38" condition="erroInterno">
                                                                                                                                                                    <jpi1:If.Body>
                                                                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_145">
                                                                                                                                                                          <Sequence DisplayName="IF RENDA 5" sap2010:WorkflowViewState.IdRef="Sequence_144">
                                                                                                                                                                            <jpc:CodePython Code="loggerBot.error(&quot;Impossível realizar exclusão. Única renda cadastrada.&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_308" />
                                                                                                                                                                            <jpc:CodePython Code="# Coordenada Clique em OK - Mensagem não é possível excluir&#xA;x, y = 1082, 596&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica em OK ***VERIFICAR COORDENADAS***" sap2010:WorkflowViewState.IdRef="CodePython_309" />
                                                                                                                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_316" InstanceBrowser="deskBot" wait_time="3000" />
                                                                                                                                                                            <jpc:CodePython Code="# Coordenada Linha 5&#xA;x, y = 1678, 355&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 5" sap2010:WorkflowViewState.IdRef="CodePython_310" />
                                                                                                                                                                            <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_317" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                                                                            <jif:Find DisplayName="Botão Alterar Renda" sap2010:WorkflowViewState.IdRef="Find_180" InBrowser="deskBot" best="True" element_coord="bt_alterarRenda" grayscale="False" height="None" label="&quot;bt_alterarRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                                                                            <jmc:Click DisplayName="Click no botão Alterar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_145" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                                                                                                            <jpc:CodePython Code="valorRenda = atualizar_renda()" DisplayName="CHAMA FUNÇÃO ATUALIZAR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_311" />
                                                                                                                                                                          </Sequence>
                                                                                                                                                                        </Sequence>
                                                                                                                                                                      </ActivityAction>
                                                                                                                                                                    </jpi1:If.Body>
                                                                                                                                                                  </jpi1:If>
                                                                                                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_19">
                                                                                                                                                                    <jpe:Else.Body>
                                                                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                                        <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_146">
                                                                                                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Renda excluída com sucesso!&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_312" />
                                                                                                                                                                          <jpc:CodePython Code="# Coordenada Linha 6&#xA;x, y = 1678, 377&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 6" sap2010:WorkflowViewState.IdRef="CodePython_313" />
                                                                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_318" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                                                                          <jpc:CodePython Code="excluir_renda()" DisplayName="CHAMA FUNÇÃO EXCLUIR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_314" />
                                                                                                                                                                          <jif:Find sap2010:WorkflowViewState.IdRef="Find_181" InBrowser="deskBot" best="True" element_coord="erroInterno" grayscale="False" height="None" label="&quot;erroInterno&quot;" matching="0.8" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                                                                          <jpi:If_Container sap2010:WorkflowViewState.IdRef="If_Container_25">
                                                                                                                                                                            <jpi:If_Container.Body>
                                                                                                                                                                              <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                                                <Sequence DisplayName="Conditional Structure" sap2010:WorkflowViewState.IdRef="Sequence_147">
                                                                                                                                                                                  <jpi1:If DisplayName="If Condition" sap2010:WorkflowViewState.IdRef="If_39" condition="erroInterno">
                                                                                                                                                                                    <jpi1:If.Body>
                                                                                                                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                                                                                                                        <Sequence DisplayName="IF RENDA 6" sap2010:WorkflowViewState.IdRef="Sequence_148">
                                                                                                                                                                                          <jpc:CodePython Code="loggerBot.error(&quot;Impossível realizar exclusão. Única renda cadastrada.&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_315" />
                                                                                                                                                                                          <jpc:CodePython Code="# Coordenada Clique em OK - Mensagem não é possível excluir&#xA;x, y = 1082, 596&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica em OK ***VERIFICAR COORDENADAS***" sap2010:WorkflowViewState.IdRef="CodePython_316" />
                                                                                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_319" InstanceBrowser="deskBot" wait_time="3000" />
                                                                                                                                                                                          <jpc:CodePython Code="# Coordenada Linha 6&#xA;x, y = 1678, 377&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica na Renda 6" sap2010:WorkflowViewState.IdRef="CodePython_317" />
                                                                                                                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_320" InstanceBrowser="deskBot" wait_time="2000" />
                                                                                                                                                                                          <jif:Find DisplayName="Botão Alterar Renda" sap2010:WorkflowViewState.IdRef="Find_182" InBrowser="deskBot" best="True" element_coord="bt_alterarRenda" grayscale="False" height="None" label="&quot;bt_alterarRenda&quot;" matching="0.9" threshold="None" waiting_time="10000" width="None" x="None" y="None" />
                                                                                                                                                                                          <jmc:Click DisplayName="Click no botão Alterar" Element="deskBot" sap2010:WorkflowViewState.IdRef="Click_146" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                                                                                                                                          <jpc:CodePython Code="valorRenda = atualizar_renda()" DisplayName="CHAMA FUNÇÃO ATUALIZAR RENDA" sap2010:WorkflowViewState.IdRef="CodePython_318" />
                                                                                                                                                                                        </Sequence>
                                                                                                                                                                                      </ActivityAction>
                                                                                                                                                                                    </jpi1:If.Body>
                                                                                                                                                                                  </jpi1:If>
                                                                                                                                                                                </Sequence>
                                                                                                                                                                              </ActivityAction>
                                                                                                                                                                            </jpi:If_Container.Body>
                                                                                                                                                                          </jpi:If_Container>
                                                                                                                                                                        </Sequence>
                                                                                                                                                                      </ActivityAction>
                                                                                                                                                                    </jpe:Else.Body>
                                                                                                                                                                  </jpe:Else>
                                                                                                                                                                </Sequence>
                                                                                                                                                              </ActivityAction>
                                                                                                                                                            </jpi:If_Container.Body>
                                                                                                                                                          </jpi:If_Container>
                                                                                                                                                        </Sequence>
                                                                                                                                                      </ActivityAction>
                                                                                                                                                    </jpe:Else.Body>
                                                                                                                                                  </jpe:Else>
                                                                                                                                                </Sequence>
                                                                                                                                              </ActivityAction>
                                                                                                                                            </jpi:If_Container.Body>
                                                                                                                                          </jpi:If_Container>
                                                                                                                                        </Sequence>
                                                                                                                                      </ActivityAction>
                                                                                                                                    </jpe:Else.Body>
                                                                                                                                  </jpe:Else>
                                                                                                                                </Sequence>
                                                                                                                              </ActivityAction>
                                                                                                                            </jpi:If_Container.Body>
                                                                                                                          </jpi:If_Container>
                                                                                                                        </Sequence>
                                                                                                                      </ActivityAction>
                                                                                                                    </jpe:Else.Body>
                                                                                                                  </jpe:Else>
                                                                                                                </Sequence>
                                                                                                              </ActivityAction>
                                                                                                            </jpi:If_Container.Body>
                                                                                                          </jpi:If_Container>
                                                                                                        </Sequence>
                                                                                                      </ActivityAction>
                                                                                                    </jpe:Else.Body>
                                                                                                  </jpe:Else>
                                                                                                </Sequence>
                                                                                              </ActivityAction>
                                                                                            </jpi:If_Container.Body>
                                                                                          </jpi:If_Container>
                                                                                        </Sequence>
                                                                                      </ActivityAction>
                                                                                    </jpi1:If.Body>
                                                                                  </jpi1:If>
                                                                                  <jpe:Else sap2010:WorkflowViewState.IdRef="Else_7">
                                                                                    <jpe:Else.Body>
                                                                                      <ActivityAction x:TypeArguments="x:String">
                                                                                        <Sequence DisplayName="NOVA RENDA" sap2010:WorkflowViewState.IdRef="Sequence_48">
                                                                                          <jpc:CodePython Code="loggerBot.error(&quot;CPF &quot;+linha[1]+&quot; não possui renda cadastrada&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_208" />
                                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Iniciando cadastro da renda...&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_209" />
                                                                                          <jpc:CodePython Code="# Coordenada Nova Renda&#xA;x, y = 1888, 916&#xA;&#xA;# Movimenta o cursor para as coordenadas e realiza o clique&#xA;win32api.SetCursorPos((x, y))  # Move o cursor para as coordenadas&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0)  # Pressiona o botão esquerdo do mouse&#xA;win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0)  # Libera o botão esquerdo do mouse&#xA;" DisplayName="Clica no botão Nova Renda" sap2010:WorkflowViewState.IdRef="CodePython_319" />
                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_206" InstanceBrowser="deskBot" wait_time="3000" />
                                                                                          <jpc:CodePython Code="valorRenda = nova_renda()" DisplayName="CHAMA FUNÇÃO NOVA RENDA" sap2010:WorkflowViewState.IdRef="CodePython_300" />
                                                                                          <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_225" InstanceBrowser="deskBot" wait_time="5000" />
                                                                                        </Sequence>
                                                                                      </ActivityAction>
                                                                                    </jpe:Else.Body>
                                                                                  </jpe:Else>
                                                                                </Sequence>
                                                                              </ActivityAction>
                                                                            </jpi:If_Container.Body>
                                                                          </jpi:If_Container>
                                                                        </Sequence>
                                                                        <Sequence DisplayName="REGISTRO DOS CPFs REALIZADOS" sap2010:WorkflowViewState.IdRef="Sequence_38">
                                                                          <jpc:CodePython Code="# Obter a data e hora atuais&#xA;data_atualizacao_final = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Data e hora atual&#xA;&#xA;cpf_update = linha[1]&#xA;&#xA;# Garantir que valorRenda seja um número float antes de qualquer operação&#xA;# Converter formato brasileiro (1.200,00) para formato americano (1200.00)&#xA;try:&#xA;    if not valorRenda or valorRenda.strip() == &quot;&quot;:&#xA;        raise ValueError(&quot;valorRenda está vazio ou nulo&quot;)&#xA;    &#xA;    # Remove pontos (separadores de milhares) e substitui vírgula por ponto&#xA;    valorRenda_limpo = valorRenda.replace('.', '').replace(',', '.')&#xA;    valorRenda_float = float(valorRenda_limpo)&#xA;    loggerBot.info(f&quot;Valor da renda convertido: {valorRenda} -&gt; {valorRenda_float}&quot;)&#xA;    &#xA;except (ValueError, AttributeError) as e:&#xA;    error_msg = f&quot;ERRO CRÍTICO: Falha ao converter valorRenda '{valorRenda}' para float: {e}&quot;&#xA;    loggerBot.error(error_msg)&#xA;    print(error_msg)&#xA;    # Re-lança o erro para interromper a execução&#xA;    raise ValueError(f&quot;Conversão de renda falhou para CPF {linha[1]}: {valorRenda} -&gt; {e}&quot;)&#xA;&#xA;valorRenda_final = f&quot;{valorRenda_float:.2f}&quot;&#xA;&#xA;cpf_encaminhado = f&quot;UPDATE associados SET status = '2', renda = '{valorRenda_final}', data_encaminhado = '{data_atualizacao_final}' WHERE cpf = '{cpf_update}'&quot;&#xA;cursor.execute(cpf_encaminhado)&#xA;conexao.commit()" DisplayName="REGISTRA ATUALIZACAO NO BANCO DE DADOS" sap2010:WorkflowViewState.IdRef="CodePython_185" />
                                                                          <jdw:Wait sap2010:WorkflowViewState.IdRef="Wait_313" InstanceBrowser="deskBot" wait_time="3000" />
                                                                          <jpc:CodePython Code="# Após registrar o CPF como concluido, mover o arquivo PDF para a pasta &quot;concluidos&quot;&#xA;try:&#xA;    # Caminho do arquivo original na pasta &quot;consultas&quot;&#xA;    arquivo_origem = os.path.join(&quot;C:\\RPA\\AtualizacaoCadastral\\consultas&quot;, f&quot;[{linha[1]}].pdf&quot;)&#xA;&#xA;    # Caminho para a pasta &quot;concluidos&quot;&#xA;    arquivo_destino = os.path.join(&quot;C:\\RPA\\AtualizacaoCadastral\\concluidos&quot;, f&quot;[{linha[1]}].pdf&quot;)&#xA;&#xA;    # Verificar se o arquivo existe na pasta &quot;consultas&quot;&#xA;    if os.path.exists(arquivo_origem):&#xA;        # Mover o arquivo para a pasta &quot;concluidos&quot;&#xA;        shutil.move(arquivo_origem, arquivo_destino)&#xA;        print(f&quot;Arquivo movido para: {arquivo_destino}&quot;)&#xA;    else:&#xA;        print(f&quot;Arquivo não encontrado para mover: {arquivo_origem}&quot;)&#xA;&#xA;except Exception as e:&#xA;    print(f&quot;Erro ao mover o arquivo: {e}&quot;)" DisplayName="Mover PDF para a pasta concluidos" sap2010:WorkflowViewState.IdRef="CodePython_187" />
                                                                          <jpc:CodePython Code="loggerBot.info(&quot;Atualização de renda para o CPF &quot;+linha[1]+&quot; encaminhada para autorização!&quot;)" DisplayName="Mensagem Log" sap2010:WorkflowViewState.IdRef="CodePython_212" />
                                                                        </Sequence>
                                                                        <jpc:CodePython Code="# Função para trazer a aplicação para frente&#xA;def bring_application_to_front():&#xA;    try:&#xA;        # Acessando a aplicação via AppActivate&#xA;        shell = win32com.client.Dispatch(&quot;WScript.Shell&quot;)&#xA;        shell.AppActivate(&quot;Google Chrome&quot;)  # Nome da janela da aplicação&#xA;        &#xA;        # Espera um pouco para garantir que a janela está ativa&#xA;        time.sleep(1)&#xA;&#xA;        # Obtém o identificador da janela pela classe ou título&#xA;        hwnd = win32gui.FindWindow(None, &quot;Google Chrome&quot;)  # Substitua &quot;Google Chrome&quot; com o título correto da janela&#xA;        &#xA;        if hwnd:&#xA;            # Garante que a janela seja trazida para frente&#xA;            win32gui.ShowWindow(hwnd, 5)  # 5 é SW_SHOW (mostrar janela)&#xA;            win32gui.SetForegroundWindow(hwnd)  # Traz a janela para frente&#xA;            print(&quot;Aplicação trazida para frente com sucesso!&quot;)&#xA;        else:&#xA;            print(&quot;Aplicação não encontrada!&quot;)&#xA;        &#xA;        # Tentativa adicional para garantir o foco&#xA;        time.sleep(1)  # Atraso antes da tentativa adicional&#xA;        if hwnd:&#xA;            win32gui.SetForegroundWindow(hwnd)  # Nova tentativa de trazer para frente&#xA;            print(&quot;Segunda tentativa de trazer a aplicação para frente.&quot;)&#xA;&#xA;    except Exception as e:&#xA;        print(f&quot;Erro ao trazer a aplicação para frente: {e}&quot;)&#xA;&#xA;# Chamada da função no ponto necessário do script&#xA;bring_application_to_front()" DisplayName="Trazer CHROME para Frente" sap2010:WorkflowViewState.IdRef="CodePython_163" />
                                                                        <jwf:Find_Element DisplayName="Botão Nova Consulta" sap2010:WorkflowViewState.IdRef="Find_Element_14" InstanceBrowser="webBot" by="XPATH" ensure_clickable="False" ensure_visible="False" instance_element="btn_novaConsulta" selector="&quot;/html/body/app-root/app-layout/app-menu/div/ul/li[1]/a/span[1]/seds-icon/mat-icon&quot;" waiting_time="1000" />
                                                                        <jmc:Click Element="btn_novaConsulta" sap2010:WorkflowViewState.IdRef="Click_95" button="Left" clicks="One" interval_between_clicks="0" wait_after="300" />
                                                                        <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_203" InstanceBrowser="webBot" wait_time="4000" />
                                                                      </Sequence>
                                                                      <jpc:CodePython Code="pass" sap2010:WorkflowViewState.IdRef="CodePython_292" />
                                                                    </Sequence>
                                                                  </ActivityAction>
                                                                </jpf:ForEach.Body>
                                                              </jpf:ForEach>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </jpi1:If.Body>
                                                      </jpi1:If>
                                                      <jpe:Else sap2010:WorkflowViewState.IdRef="Else_17">
                                                        <jpe:Else.Body>
                                                          <ActivityAction x:TypeArguments="x:String">
                                                            <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_139">
                                                              <jpc:CodePython Code="conexao.commit()&#xA;loggerBot.info(&quot;Nenhum registro disponível para processamento&quot;)&#xA;break  # Sai do loop se não encontrou registros" sap2010:WorkflowViewState.IdRef="CodePython_289" />
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </jpe:Else.Body>
                                                      </jpe:Else>
                                                    </Sequence>
                                                  </ActivityAction>
                                                </jpi:If_Container.Body>
                                              </jpi:If_Container>
                                            </Sequence>
                                          </ActivityAction>
                                        </jpt1:Try.Body>
                                      </jpt1:Try>
                                      <jpt2:Try_Catch DisplayName="Catch" sap2010:WorkflowViewState.IdRef="Try_Catch_3" condition="Exception" variable="e">
                                        <jpt2:Try_Catch.Body>
                                          <ActivityAction x:TypeArguments="x:String">
                                            <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_134">
                                              <jpc:CodePython Code="# Em caso de erro, faz rollback&#xA;conexao.rollback()&#xA;loggerBot.error(f&quot;Erro ao processar registro: {str(e)}&quot;)&#xA;raise e" sap2010:WorkflowViewState.IdRef="CodePython_290" />
                                            </Sequence>
                                          </ActivityAction>
                                        </jpt2:Try_Catch.Body>
                                      </jpt2:Try_Catch>
                                    </Sequence>
                                  </ActivityAction>
                                </jpt:Try_Container.Body>
                              </jpt:Try_Container>
                            </Sequence>
                          </ActivityAction>
                        </jpw1:While.Body>
                      </jpw1:While>
                      <jpw:Wait sap2010:WorkflowViewState.IdRef="Wait_205" InstanceBrowser="webBot" wait_time="5000" />
                    </Sequence>
                  </Sequence>
                </ActivityAction>
              </jpt1:Try.Body>
            </jpt1:Try>
            <jpt2:Try_Catch DisplayName="Catch" sap2010:WorkflowViewState.IdRef="Try_Catch_2" condition="Exception" variable="ex">
              <jpt2:Try_Catch.Body>
                <ActivityAction x:TypeArguments="x:String">
                  <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_70">
                    <jpc:CodePython Code="loggerBot.error(&quot;Erro durante a execução do bot&quot;, exc_info=True)&#xA;&#xA;# Criar diretório para as screenshots, caso não exista&#xA;screenshot_dir = &quot;C:\\RPA\\AtualizacaoCadastral\\logs\\screenshots&quot;&#xA;if not os.path.exists(screenshot_dir):&#xA;     os.makedirs(screenshot_dir)&#xA;            &#xA;# Nome do arquivo de screenshot com data e hora para garantir que seja único&#xA;screenshot_filename = datetime.now().strftime(&quot;%Y-%m-%d_%H-%M-%S&quot;) + &quot;_error.png&quot;&#xA;screenshot_path = os.path.join(screenshot_dir, screenshot_filename)&#xA;            &#xA;# Capturar a screenshot e salvar no diretório&#xA;pyautogui.screenshot(screenshot_path)&#xA;loggerBot.info(f&quot;Screenshot salva em: {screenshot_path}&quot;)" DisplayName="Tira Screenshot e captura os logs" sap2010:WorkflowViewState.IdRef="CodePython_190" />
                    <Sequence DisplayName="ALTERA STATUS DO CPF PARA NAO ENCAMINHADO NO BANCO DE DADOS" sap2010:WorkflowViewState.IdRef="Sequence_76">
                      <jpc:CodePython Code="if 'linha' in locals() and linha:&#xA;     loggerBot.error(&quot;CPF &quot;+linha[1]+&quot; não encaminhado para atualização.&quot;)&#xA;&#xA;     # Atualizar status para 4&#xA;     data_atualizacao_final2 = datetime.now().strftime('%Y-%m-%d %H:%M:%S')&#xA;     cpf_update2 = linha[1]&#xA;                        &#xA;     cpf_naoencaminhado = f&quot;UPDATE associados SET status = '4', data_atualizacao = '{data_atualizacao_final2}' WHERE cpf = '{cpf_update2}'&quot;&#xA;     cursor.execute(cpf_naoencaminhado)&#xA;     conexao.commit()" sap2010:WorkflowViewState.IdRef="CodePython_284" />
                    </Sequence>
                  </Sequence>
                </ActivityAction>
              </jpt2:Try_Catch.Body>
            </jpt2:Try_Catch>
            <jpt2:Try_Catch sap2010:WorkflowViewState.IdRef="Try_Catch_4" condition="Exception" variable="ex">
              <jpt2:Try_Catch.Body>
                <ActivityAction x:TypeArguments="x:String">
                  <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_140">
                    <jpc:CodePython Code=" # Tratamento de erro global&#xA;loggerBot.error(&quot;Erro crítico durante a execução do bot&quot;, exc_info=True)&#xA;raise ex" sap2010:WorkflowViewState.IdRef="CodePython_291" />
                  </Sequence>
                </ActivityAction>
              </jpt2:Try_Catch.Body>
            </jpt2:Try_Catch>
            <jpt3:Try_Finally DisplayName="Finally" sap2010:WorkflowViewState.IdRef="Try_Finally_2">
              <jpt3:Try_Finally.Body>
                <ActivityAction x:TypeArguments="x:String">
                  <Sequence DisplayName="Body" sap2010:WorkflowViewState.IdRef="Sequence_71">
                    <jpc:CodePython Code="# Encerrar os handlers para liberar o arquivo&#xA;for handler in loggerBot.handlers:&#xA;     handler.close()&#xA;     loggerBot.removeHandler(handler)" sap2010:WorkflowViewState.IdRef="CodePython_191" />
                  </Sequence>
                </ActivityAction>
              </jpt3:Try_Finally.Body>
            </jpt3:Try_Finally>
          </Sequence>
        </ActivityAction>
      </jpt:Try_Container.Body>
    </jpt:Try_Container>
    <sads:DebugSymbol.Symbol>dydDOlxSUEFcQXR1YWxpemFjYW9DYWRhc3RyYWwgdjJcQm90LnhhbWwHIAP2BQ4CAQEhBSGJCwIBByIFIrEFAgEGIwUjlXsCAQUkBSTnfAIBBCUFJfYIAgEDJgX0BRkCAQI=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="CodePython_189" sap:VirtualizedContainerService.HintSize="1302,81" />
      <sap2010:ViewStateData Id="CodePython_264" sap:VirtualizedContainerService.HintSize="1302,81" />
      <sap2010:ViewStateData Id="CodePython_294" sap:VirtualizedContainerService.HintSize="1302,81" />
      <sap2010:ViewStateData Id="CodePython_320" sap:VirtualizedContainerService.HintSize="1302,81" />
      <sap2010:ViewStateData Id="CodePython_301" sap:VirtualizedContainerService.HintSize="1302,81" />
      <sap2010:ViewStateData Id="CodePython_194" sap:VirtualizedContainerService.HintSize="1208,78" />
      <sap2010:ViewStateData Id="Load_DotENV_1" sap:VirtualizedContainerService.HintSize="490,22" />
      <sap2010:ViewStateData Id="Get_DotEnv_Value_1" sap:VirtualizedContainerService.HintSize="490,119" />
      <sap2010:ViewStateData Id="Get_DotEnv_Value_2" sap:VirtualizedContainerService.HintSize="490,119" />
      <sap2010:ViewStateData Id="Get_DotEnv_Value_5" sap:VirtualizedContainerService.HintSize="490,119" />
      <sap2010:ViewStateData Id="Get_DotEnv_Value_6" sap:VirtualizedContainerService.HintSize="490,119" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="1208,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="OpenApplication_1" sap:VirtualizedContainerService.HintSize="1208,217" />
      <sap2010:ViewStateData Id="CodePython_266" sap:VirtualizedContainerService.HintSize="1208,78" />
      <sap2010:ViewStateData Id="Wait_251" sap:VirtualizedContainerService.HintSize="1208,96" />
      <sap2010:ViewStateData Id="CodePython_236" sap:VirtualizedContainerService.HintSize="1208,81" />
      <sap2010:ViewStateData Id="Wait_252" sap:VirtualizedContainerService.HintSize="1208,96" />
      <sap2010:ViewStateData Id="Send_Hotkey_84" sap:VirtualizedContainerService.HintSize="1208,117" />
      <sap2010:ViewStateData Id="Type_Into_53" sap:VirtualizedContainerService.HintSize="1208,123" />
      <sap2010:ViewStateData Id="CodePython_237" sap:VirtualizedContainerService.HintSize="1208,81" />
      <sap2010:ViewStateData Id="Type_Into_54" sap:VirtualizedContainerService.HintSize="1208,123" />
      <sap2010:ViewStateData Id="Find_127" sap:VirtualizedContainerService.HintSize="1208,155" />
      <sap2010:ViewStateData Id="Click_113" sap:VirtualizedContainerService.HintSize="1208,81" />
      <sap2010:ViewStateData Id="Wait_253" sap:VirtualizedContainerService.HintSize="1208,96" />
      <sap2010:ViewStateData Id="Find_170" sap:VirtualizedContainerService.HintSize="456,155" />
      <sap2010:ViewStateData Id="CodePython_267" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_129" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="420,305" />
      <sap2010:ViewStateData Id="CodePython_268" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_16" sap:VirtualizedContainerService.HintSize="420,293" />
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="442,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_22" sap:VirtualizedContainerService.HintSize="456,871" />
      <sap2010:ViewStateData Id="Sequence_128" sap:VirtualizedContainerService.HintSize="1208,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="OpenBrowser_1" sap:VirtualizedContainerService.HintSize="400,174" />
      <sap2010:ViewStateData Id="CodePython_269" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="CodePython_270" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="Wait_1" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_Element_1" sap:VirtualizedContainerService.HintSize="400,177" />
      <sap2010:ViewStateData Id="Click_1" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Type_Into_55" sap:VirtualizedContainerService.HintSize="400,123" />
      <sap2010:ViewStateData Id="Find_Element_2" sap:VirtualizedContainerService.HintSize="400,177" />
      <sap2010:ViewStateData Id="Click_2" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Type_Into_56" sap:VirtualizedContainerService.HintSize="400,123" />
      <sap2010:ViewStateData Id="Wait_2" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_Element_3" sap:VirtualizedContainerService.HintSize="400,177" />
      <sap2010:ViewStateData Id="Click_3" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Wait_3" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_Element_5" sap:VirtualizedContainerService.HintSize="400,177" />
      <sap2010:ViewStateData Id="CodePython_192" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="420,305" />
      <sap2010:ViewStateData Id="CodePython_193" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_8" sap:VirtualizedContainerService.HintSize="420,293" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="442,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_12" sap:VirtualizedContainerService.HintSize="400,84">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Click_4" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Wait_5" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_Element_6" sap:VirtualizedContainerService.HintSize="400,177" />
      <sap2010:ViewStateData Id="Click_5" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="CodePython_271" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="Wait_6" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_Element_7" sap:VirtualizedContainerService.HintSize="400,177" />
      <sap2010:ViewStateData Id="Click_6" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="CodePython_272" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="Wait_7" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="CodePython_273" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="CodePython_186" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="CodePython_286" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="1208,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_265" sap:VirtualizedContainerService.HintSize="1172,81" />
      <sap2010:ViewStateData Id="CodePython_287" sap:VirtualizedContainerService.HintSize="1100,81" />
      <sap2010:ViewStateData Id="CodePython_288" sap:VirtualizedContainerService.HintSize="998,81" />
      <sap2010:ViewStateData Id="CodePython_277" sap:VirtualizedContainerService.HintSize="940,78" />
      <sap2010:ViewStateData Id="Find_Element_8" sap:VirtualizedContainerService.HintSize="940,177" />
      <sap2010:ViewStateData Id="Click_7" sap:VirtualizedContainerService.HintSize="940,81" />
      <sap2010:ViewStateData Id="Wait_12" sap:VirtualizedContainerService.HintSize="940,96" />
      <sap2010:ViewStateData Id="Send_Hotkey_1" sap:VirtualizedContainerService.HintSize="940,117">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Type_Into_60" sap:VirtualizedContainerService.HintSize="940,123" />
      <sap2010:ViewStateData Id="Wait_4" sap:VirtualizedContainerService.HintSize="940,96" />
      <sap2010:ViewStateData Id="Find_Element_9" sap:VirtualizedContainerService.HintSize="940,177" />
      <sap2010:ViewStateData Id="Click_8" sap:VirtualizedContainerService.HintSize="940,81" />
      <sap2010:ViewStateData Id="Wait_9" sap:VirtualizedContainerService.HintSize="940,96" />
      <sap2010:ViewStateData Id="Find_1" sap:VirtualizedContainerService.HintSize="940,155" />
      <sap2010:ViewStateData Id="Find_Element_12" sap:VirtualizedContainerService.HintSize="200,177" />
      <sap2010:ViewStateData Id="Click_11" sap:VirtualizedContainerService.HintSize="200,81" />
      <sap2010:ViewStateData Id="Find_Element_13" sap:VirtualizedContainerService.HintSize="200,177" />
      <sap2010:ViewStateData Id="Click_12" sap:VirtualizedContainerService.HintSize="200,81" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="222,760">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="404,863">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="426,987">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="448,1111">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_1" sap:VirtualizedContainerService.HintSize="940,84">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Wait_13" sap:VirtualizedContainerService.HintSize="940,96" />
      <sap2010:ViewStateData Id="Find_Element_10" sap:VirtualizedContainerService.HintSize="940,177" />
      <sap2010:ViewStateData Id="CodePython_195" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="420,305" />
      <sap2010:ViewStateData Id="CodePython_196" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_9" sap:VirtualizedContainerService.HintSize="420,293" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="442,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_13" sap:VirtualizedContainerService.HintSize="940,84">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Click_9" sap:VirtualizedContainerService.HintSize="940,81" />
      <sap2010:ViewStateData Id="Wait_321" sap:VirtualizedContainerService.HintSize="940,96" />
      <sap2010:ViewStateData Id="CodePython_213" sap:VirtualizedContainerService.HintSize="918,78" />
      <sap2010:ViewStateData Id="DesktopBot_3" sap:VirtualizedContainerService.HintSize="918,91" />
      <sap2010:ViewStateData Id="Wait_230" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_81" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_102" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_82" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_103" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_278" sap:VirtualizedContainerService.HintSize="918,78" />
      <sap2010:ViewStateData Id="Type_Into_58" sap:VirtualizedContainerService.HintSize="918,123" />
      <sap2010:ViewStateData Id="Wait_254" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="Find_62" sap:VirtualizedContainerService.HintSize="918,155" />
      <sap2010:ViewStateData Id="Click_54" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_105" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="Find_63" sap:VirtualizedContainerService.HintSize="918,155" />
      <sap2010:ViewStateData Id="CodePython_197" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_57" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="420,305" />
      <sap2010:ViewStateData Id="CodePython_198" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_10" sap:VirtualizedContainerService.HintSize="420,293" />
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="442,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_14" sap:VirtualizedContainerService.HintSize="918,84">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Click_55" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_250" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="Click_112" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Type_Into_61" sap:VirtualizedContainerService.HintSize="918,123" />
      <sap2010:ViewStateData Id="Wait_106" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="Find_64" sap:VirtualizedContainerService.HintSize="918,155" />
      <sap2010:ViewStateData Id="Click_56" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Click_57" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="CodePython_279" sap:VirtualizedContainerService.HintSize="918,78" />
      <sap2010:ViewStateData Id="Wait_248" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_257" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_305" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_255" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_303" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_258" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_304" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_260" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="CodePython_280" sap:VirtualizedContainerService.HintSize="918,78" />
      <sap2010:ViewStateData Id="Wait_276" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_248" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Wait_277" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Type_Into_89" sap:VirtualizedContainerService.HintSize="400,123">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Wait_278" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_146" sap:VirtualizedContainerService.HintSize="400,155" />
      <sap2010:ViewStateData Id="Click_122" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="CodePython_281" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="Wait_279" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_147" sap:VirtualizedContainerService.HintSize="400,155" />
      <sap2010:ViewStateData Id="CodePython_249" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_100" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="420,305" />
      <sap2010:ViewStateData Id="CodePython_250" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_101" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_14" sap:VirtualizedContainerService.HintSize="420,293" />
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="442,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_19" sap:VirtualizedContainerService.HintSize="400,84">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Click_123" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Wait_280" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Click_124" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Type_Into_90" sap:VirtualizedContainerService.HintSize="400,123" />
      <sap2010:ViewStateData Id="Wait_281" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="Find_148" sap:VirtualizedContainerService.HintSize="400,155" />
      <sap2010:ViewStateData Id="Click_125" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Click_126" sap:VirtualizedContainerService.HintSize="400,81" />
      <sap2010:ViewStateData Id="Wait_282" sap:VirtualizedContainerService.HintSize="400,96" />
      <sap2010:ViewStateData Id="CodePython_282" sap:VirtualizedContainerService.HintSize="400,78" />
      <sap2010:ViewStateData Id="Sequence_103" sap:VirtualizedContainerService.HintSize="918,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_261" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_309" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_262" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_310" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_263" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_312" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Sequence_127" sap:VirtualizedContainerService.HintSize="918,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_86" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_111" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="Find_65" sap:VirtualizedContainerService.HintSize="918,155" />
      <sap2010:ViewStateData Id="CodePython_199" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="420,305" />
      <sap2010:ViewStateData Id="CodePython_200" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="362,202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_11" sap:VirtualizedContainerService.HintSize="420,293" />
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="442,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_15" sap:VirtualizedContainerService.HintSize="918,871">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Click_58" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Wait_112" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="CodePython_87" sap:VirtualizedContainerService.HintSize="918,81" />
      <sap2010:ViewStateData Id="Find_125" sap:VirtualizedContainerService.HintSize="918,155" />
      <sap2010:ViewStateData Id="CodePython_232" sap:VirtualizedContainerService.HintSize="362,81" />
      <sap2010:ViewStateData Id="Wait_245" sap:VirtualizedContainerService.HintSize="362,96" />
      <sap2010:ViewStateData Id="CodePython_231" sap:VirtualizedContainerService.HintSize="362,81" />
      <sap2010:ViewStateData Id="Wait_247" sap:VirtualizedContainerService.HintSize="362,96" />
      <sap2010:ViewStateData Id="Find_Element_19" sap:VirtualizedContainerService.HintSize="362,177" />
      <sap2010:ViewStateData Id="Click_111" sap:VirtualizedContainerService.HintSize="362,81" />
      <sap2010:ViewStateData Id="Wait_246" sap:VirtualizedContainerService.HintSize="362,96" />
      <sap2010:ViewStateData Id="CodePython_230" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="362,205">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_229" sap:VirtualizedContainerService.HintSize="362,78" />
      <sap2010:ViewStateData Id="Continue_2" sap:VirtualizedContainerService.HintSize="362,51" />
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="384,1526">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="918,1629">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Wait_113" sap:VirtualizedContainerService.HintSize="918,96" />
      <sap2010:ViewStateData Id="Find_114" sap:VirtualizedContainerService.HintSize="918,155" />
      <sap2010:ViewStateData Id="CodePython_302" sap:VirtualizedContainerService.HintSize="816,78" />
      <sap2010:ViewStateData Id="Find_175" sap:VirtualizedContainerService.HintSize="816,155" />
      <sap2010:ViewStateData Id="CodePython_201" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_88" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_114" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_89" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_115" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Find_173" sap:VirtualizedContainerService.HintSize="340,155" />
      <sap2010:ViewStateData Id="Click_144" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="CodePython_295" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="780,154">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_202" sap:VirtualizedContainerService.HintSize="744,78" />
      <sap2010:ViewStateData Id="CodePython_106" sap:VirtualizedContainerService.HintSize="744,81" />
      <sap2010:ViewStateData Id="Wait_136" sap:VirtualizedContainerService.HintSize="744,96" />
      <sap2010:ViewStateData Id="CodePython_303" sap:VirtualizedContainerService.HintSize="744,78" />
      <sap2010:ViewStateData Id="Find_176" sap:VirtualizedContainerService.HintSize="744,155" />
      <sap2010:ViewStateData Id="CodePython_203" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_107" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_137" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_108" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_314" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Find_172" sap:VirtualizedContainerService.HintSize="340,155" />
      <sap2010:ViewStateData Id="Click_143" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="CodePython_296" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="708,154" />
      <sap2010:ViewStateData Id="CodePython_204" sap:VirtualizedContainerService.HintSize="672,78" />
      <sap2010:ViewStateData Id="CodePython_125" sap:VirtualizedContainerService.HintSize="672,81" />
      <sap2010:ViewStateData Id="Wait_158" sap:VirtualizedContainerService.HintSize="672,96" />
      <sap2010:ViewStateData Id="CodePython_304" sap:VirtualizedContainerService.HintSize="672,78" />
      <sap2010:ViewStateData Id="Find_177" sap:VirtualizedContainerService.HintSize="672,155" />
      <sap2010:ViewStateData Id="CodePython_205" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_126" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_159" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_127" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_160" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Find_171" sap:VirtualizedContainerService.HintSize="340,155" />
      <sap2010:ViewStateData Id="Click_142" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="CodePython_297" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="636,154">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_206" sap:VirtualizedContainerService.HintSize="600,78" />
      <sap2010:ViewStateData Id="CodePython_144" sap:VirtualizedContainerService.HintSize="600,81" />
      <sap2010:ViewStateData Id="Wait_180" sap:VirtualizedContainerService.HintSize="600,96" />
      <sap2010:ViewStateData Id="CodePython_298" sap:VirtualizedContainerService.HintSize="600,78" />
      <sap2010:ViewStateData Id="Find_178" sap:VirtualizedContainerService.HintSize="600,155" />
      <sap2010:ViewStateData Id="CodePython_207" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_145" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_181" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_146" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_182" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Find_107" sap:VirtualizedContainerService.HintSize="340,155" />
      <sap2010:ViewStateData Id="Click_88" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="CodePython_299" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="222,175">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="564,278" />
      <sap2010:ViewStateData Id="CodePython_305" sap:VirtualizedContainerService.HintSize="528,78" />
      <sap2010:ViewStateData Id="CodePython_306" sap:VirtualizedContainerService.HintSize="528,81" />
      <sap2010:ViewStateData Id="Wait_315" sap:VirtualizedContainerService.HintSize="528,96" />
      <sap2010:ViewStateData Id="CodePython_307" sap:VirtualizedContainerService.HintSize="528,78" />
      <sap2010:ViewStateData Id="Find_179" sap:VirtualizedContainerService.HintSize="528,155" />
      <sap2010:ViewStateData Id="CodePython_308" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_309" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_316" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_310" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_317" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Find_180" sap:VirtualizedContainerService.HintSize="340,155" />
      <sap2010:ViewStateData Id="Click_145" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="CodePython_311" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_144" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_145" sap:VirtualizedContainerService.HintSize="222,175">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="492,278" />
      <sap2010:ViewStateData Id="CodePython_312" sap:VirtualizedContainerService.HintSize="456,78" />
      <sap2010:ViewStateData Id="CodePython_313" sap:VirtualizedContainerService.HintSize="456,81" />
      <sap2010:ViewStateData Id="Wait_318" sap:VirtualizedContainerService.HintSize="456,96" />
      <sap2010:ViewStateData Id="CodePython_314" sap:VirtualizedContainerService.HintSize="456,78" />
      <sap2010:ViewStateData Id="Find_181" sap:VirtualizedContainerService.HintSize="456,155" />
      <sap2010:ViewStateData Id="CodePython_315" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_316" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_319" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_317" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_320" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Find_182" sap:VirtualizedContainerService.HintSize="340,155" />
      <sap2010:ViewStateData Id="Click_146" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="CodePython_318" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="420,154" />
      <sap2010:ViewStateData Id="Sequence_147" sap:VirtualizedContainerService.HintSize="442,278">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_25" sap:VirtualizedContainerService.HintSize="456,387" />
      <sap2010:ViewStateData Id="Sequence_146" sap:VirtualizedContainerService.HintSize="478,1199">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_19" sap:VirtualizedContainerService.HintSize="492,1290" />
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="514,1732">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_24" sap:VirtualizedContainerService.HintSize="528,1841" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="550,2653">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_18" sap:VirtualizedContainerService.HintSize="564,2744" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="586,3186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_6" sap:VirtualizedContainerService.HintSize="600,3295" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="622,4107">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_4" sap:VirtualizedContainerService.HintSize="636,4198" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="658,4516">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_7" sap:VirtualizedContainerService.HintSize="672,4625" />
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="694,5437">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_5" sap:VirtualizedContainerService.HintSize="708,5528">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="730,5846">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_8" sap:VirtualizedContainerService.HintSize="744,5955" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="766,6767">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_6" sap:VirtualizedContainerService.HintSize="780,6858">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="802,7176">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_9" sap:VirtualizedContainerService.HintSize="816,7285">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="838,7722">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="882,7825" />
      <sap2010:ViewStateData Id="CodePython_208" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_209" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="CodePython_319" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_206" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_300" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Wait_225" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="362,831">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_7" sap:VirtualizedContainerService.HintSize="882,922" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="904,8911">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_10" sap:VirtualizedContainerService.HintSize="918,9020" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="940,17863">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_185" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Wait_313" sap:VirtualizedContainerService.HintSize="340,96" />
      <sap2010:ViewStateData Id="CodePython_187" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="CodePython_212" sap:VirtualizedContainerService.HintSize="340,78" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="940,580">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_163" sap:VirtualizedContainerService.HintSize="940,81" />
      <sap2010:ViewStateData Id="Find_Element_14" sap:VirtualizedContainerService.HintSize="940,177" />
      <sap2010:ViewStateData Id="Click_95" sap:VirtualizedContainerService.HintSize="940,81" />
      <sap2010:ViewStateData Id="Wait_203" sap:VirtualizedContainerService.HintSize="940,96" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="962,21777">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CodePython_292" sap:VirtualizedContainerService.HintSize="962,78" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="984,22019">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach_1" sap:VirtualizedContainerService.HintSize="998,22125">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_137" sap:VirtualizedContainerService.HintSize="1020,22370">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="1064,22473" />
      <sap2010:ViewStateData Id="CodePython_289" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="362,205">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Else_17" sap:VirtualizedContainerService.HintSize="1064,296" />
      <sap2010:ViewStateData Id="Sequence_138" sap:VirtualizedContainerService.HintSize="1086,22933">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_Container_23" sap:VirtualizedContainerService.HintSize="1100,23042" />
      <sap2010:ViewStateData Id="Sequence_133" sap:VirtualizedContainerService.HintSize="1122,23287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_3" sap:VirtualizedContainerService.HintSize="1136,23378" />
      <sap2010:ViewStateData Id="CodePython_290" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="362,205">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_Catch_3" sap:VirtualizedContainerService.HintSize="1136,311" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="1158,23853">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_Container_3" sap:VirtualizedContainerService.HintSize="1172,23962" />
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="1194,24207">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="1208,24310" />
      <sap2010:ViewStateData Id="Wait_205" sap:VirtualizedContainerService.HintSize="1208,96" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1230,26785">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="1252,26909">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_2" sap:VirtualizedContainerService.HintSize="1266,27000" />
      <sap2010:ViewStateData Id="CodePython_190" sap:VirtualizedContainerService.HintSize="362,81" />
      <sap2010:ViewStateData Id="CodePython_284" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="362,205">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_70" sap:VirtualizedContainerService.HintSize="384,450">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_Catch_2" sap:VirtualizedContainerService.HintSize="1266,556" />
      <sap2010:ViewStateData Id="CodePython_291" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="362,205">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_Catch_4" sap:VirtualizedContainerService.HintSize="1266,311" />
      <sap2010:ViewStateData Id="CodePython_191" sap:VirtualizedContainerService.HintSize="340,81" />
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="362,205">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_Finally_2" sap:VirtualizedContainerService.HintSize="1266,296" />
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="1288,28407">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Try_Container_2" sap:VirtualizedContainerService.HintSize="1302,28516" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="1324,29245">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="JornadaRPAStudio.Workflow_1" sap:VirtualizedContainerService.HintSize="1364,29325" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>