#define MyAppVersion "1.20.7"
[Setup]
AppName=AtualizacaoCadastral
AppVersion={#MyAppVersion}
DefaultDirName=C:\RPA\AtualizacaoCadastral
OutputDir=.
OutputBaseFilename=AtualizacaoCadastral_Installer_{#MyAppVersion}
Compression=lzma
SolidCompression=yes
DisableProgramGroupPage=yes
SetupIconFile=Sicoob.ico
[Files]
Source: "dist\bot.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "AtualizacaoCadastral.jproj"; DestDir: "{app}"; Flags: ignoreversion
Source: ".env"; DestDir: "{app}"; Flags: ignoreversion
Source: "install_requirements.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "requirements.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "requirements.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "resources\*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs
Source: "concluidos\*"; DestDir: "{app}\concluidos"; Flags: ignoreversion recursesubdirs
Source: "consultas\*"; DestDir: "{app}\consultas"; Flags: ignoreversion recursesubdirs
Source: "logs\*"; DestDir: "{app}\logs"; Flags: ignoreversion recursesubdirs
Source: "drivers\*"; DestDir: "{app}\drivers"; Flags: ignoreversion recursesubdirs
Source: "Sicoob.ico"; DestDir: "{app}"; Flags: ignoreversion
[Icons]
Name: "{autoprograms}\AtualizacaoCadastral"; Filename: "{app}\bot.exe"; IconFilename: "{app}\Sicoob.ico"
Name: "{commondesktop}\AtualizacaoCadastral"; Filename: "{app}\bot.exe"; IconFilename: "{app}\Sicoob.ico"
[Registry]
