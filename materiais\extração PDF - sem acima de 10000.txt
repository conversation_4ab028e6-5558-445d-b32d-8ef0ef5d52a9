# Verifique o conteúdo da lista 'linha'
print(f"Conteúdo da lista 'linha': {linha}")

# Verificar se a lista 'linha' tem pelo menos 3 elementos
if len(linha) >= 3:
    # Construir o caminho do arquivo
    pdf_path = f"C:\\RPA\\AtualizacaoCadastral\\consultas\\[{str(linha[2]).strip()}].pdf"
    
    # Verificar se o arquivo existe
    if os.path.exists(pdf_path):
        # Abrir o PDF
        instancePDF = pdfplumber.open(pdf_path)
        
        # Inicializar uma lista para armazenar os valores encontrados
        valores = []

        # Iterar sobre todas as páginas do PDF
        for page in instancePDF.pages:
            # Extrair texto da página
            extractTextResult = page.extract_text(
                x_tolerance=3, 
                y_tolerance=3, 
                layout=False, 
                x_density=7.25, 
                y_density=13
            )
            
            # Normalizar o texto (substituindo quebras de linha e múltiplos espaços por um único espaço)
            extractTextResult = ' '.join(extractTextResult.split())
            
            # Imprimir o texto extraído para depuração
            print(f"Texto extraído da página:\n{extractTextResult}\n")

            # Verificar se "entre R$" está no texto
            if "entre R$" in extractTextResult:
                print("A frase 'entre R$' foi encontrada no texto.")
            else:
                print("A frase 'entre R$' NÃO foi encontrada no texto.")

            # Aplicar a regex ajustada para capturar ambos os valores
            padrao = re.search(r"entre\s+R\$\s*(\d{1,3}(?:\.\d{3})*)\s+e\s+R\$\s*(\d{1,3}(?:\.\d{3})*)", extractTextResult)

            if padrao:
                print(f"Valor 1 encontrado: {padrao.group(1)}")
                print(f"Valor 2 encontrado: {padrao.group(2)}")
                valores.append(padrao.group(2))  # Armazena o segundo valor encontrado
            else:
                print("Nenhum valor encontrado.")

        # Verificar se encontrou dois valores e pegar o segundo
        if len(valores) >= 1:
            valorRenda = valores[0]  # Pega o segundo valor encontrado
            print(f"Valor de renda encontrado: {valorRenda}")
        else:
            print("Não foram encontrados dois valores conforme esperado.")
    else:
        print(f"Erro: O arquivo PDF não foi encontrado: {pdf_path}")
else:
    print(f"Erro: A lista 'linha' não possui 3 elementos. Tamanho atual: {len(linha)}")